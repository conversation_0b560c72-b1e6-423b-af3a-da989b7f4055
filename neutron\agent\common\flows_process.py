#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import netaddr
from neutron_lib import constants
from oslo_config import cfg
from oslo_log import log as logging
from ryu.lib.packet import arp
from ryu.lib.packet import ether_types
from ryu.lib.packet import icmp as icmpv4
from ryu.lib.packet import icmpv6
from ryu.lib.packet import in_proto

from neutron.common import constants as common_constants
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_const

LOG = logging.getLogger(__name__)


class BridgeNetworkingDataPathFlows(object):
    REQUIRED_PROTOCOLS = [
        p_const.OPENFLOW10,
        p_const.OPENFLOW11,
        p_const.OPENFLOW12,
        p_const.OPENFLOW13,
        p_const.OPENFLOW14,
    ]

    def set_path_br(self, path_br):
        self.path_br = path_br
        self.path_br.add_protocols(
            *BridgeNetworkingDataPathFlows.REQUIRED_PROTOCOLS)
        self.path_br.use_at_least_protocol(p_const.OPENFLOW13)

    @staticmethod
    def get_arp_responder_match(ofp, ofpp, ip, vlan=None, in_port=None,
                                arp_op=arp.ARP_REQUEST):
        if vlan and not in_port:
            match = ofpp.OFPMatch(vlan_vid=vlan | ofp.OFPVID_PRESENT,
                                  eth_type=ether_types.ETH_TYPE_ARP,
                                  arp_op=arp_op,
                                  arp_tpa=ip)
        elif vlan and in_port:
            match = ofpp.OFPMatch(vlan_vid=vlan | ofp.OFPVID_PRESENT,
                                  in_port=in_port,
                                  eth_type=ether_types.ETH_TYPE_ARP,
                                  arp_op=arp_op,
                                  arp_tpa=ip)
        elif not vlan and in_port:
            match = ofpp.OFPMatch(in_port=in_port,
                                  eth_type=ether_types.ETH_TYPE_ARP,
                                  arp_op=arp_op,
                                  arp_tpa=ip)
        else:
            match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                  arp_op=arp_op,
                                  arp_tpa=ip)
        return match

    def install_arp_responder(self, ip, mac, bridge=None, table=None,
                              vlan=None, in_port=None):
        br = bridge if bridge else self.path_br
        table_id = (table if table is not None else
                    p_const.NP_PROVIDER_IP_ARP_RESPONDER)
        (_dp, ofp, ofpp) = br._get_dp()
        match = self.get_arp_responder_match(ofp, ofpp, ip, vlan, in_port)
        actions = [ofpp.OFPActionSetField(arp_op=arp.ARP_REPLY),
                   ofpp.NXActionRegMove(src_field='arp_sha',
                                        dst_field='arp_tha',
                                        n_bits=48),
                   ofpp.NXActionRegMove(src_field='arp_spa',
                                        dst_field='arp_tpa',
                                        n_bits=32),
                   ofpp.OFPActionSetField(arp_sha=mac),
                   ofpp.OFPActionSetField(arp_spa=ip),
                   ofpp.NXActionRegMove(src_field='eth_src',
                                        dst_field='eth_dst',
                                        n_bits=48),
                   ofpp.OFPActionSetField(eth_src=mac),
                   ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)]
        br.install_apply_actions(
            table_id=table_id,
            priority=200,
            match=match,
            actions=actions)

    def delete_arp_responder(self, ip, bridge=None, table=None,
                             vlan=None, in_port=None):
        br = bridge if bridge else self.path_br
        table_id = (table if table is not None else
                    p_const.NP_PROVIDER_IP_ARP_RESPONDER)
        (_dp, ofp, ofpp) = br._get_dp()
        match = self.get_arp_responder_match(ofp, ofpp, ip, vlan, in_port)
        br.uninstall_flows(table_id=table_id, match=match)

    def add_flow_snat_br_nat(self, vlan, src_mac, src_ip,
                             provider_mac, provider_ip,
                             reg_id=None):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(vlan_vid=vlan | ofp.OFPVID_PRESENT,
                              eth_type=ether_types.ETH_TYPE_IP,
                              eth_src=src_mac,
                              ipv4_src=src_ip)
        actions = [
            ofpp.OFPActionPopVlan(),
            ofpp.OFPActionSetField(eth_src=provider_mac),
            ofpp.OFPActionSetField(ipv4_src=provider_ip),
        ]
        if reg_id:
            actions.append(ofpp.OFPActionSetField(reg7=reg_id))
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_EGRESS_DEST_MAC_LEARN)
        ]
        self.path_br.install_instructions(table_id=p_const.NP_EGRESS_NAT,
                                          priority=201,
                                          instructions=instructions,
                                          match=match)

    def add_flow_snat_br_ingress_direct_to_int_metadata(
            self, vlan, provider_vlan, provider_ip,
            dst_mac, dst_ip, patch_ofport,
            metadata_ofport):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(
            vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
            in_port=metadata_ofport,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=provider_ip,
            ip_proto=in_proto.IPPROTO_TCP)
        actions = [
            ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
            ofpp.OFPActionSetField(eth_dst=dst_mac),
            ofpp.OFPActionSetField(
                ipv4_src=common_constants.METADATA_DEFAULT_IP),
            ofpp.OFPActionSetField(ipv4_dst=dst_ip),
            ofpp.OFPActionSetField(
                tcp_src=common_constants.METADATA_DEFAULT_PORT),
            ofpp.OFPActionOutput(patch_ofport, 0),
        ]
        instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(
            table_id=p_const.NP_INGRESS_DST_DIRECT,
            priority=202,
            instructions=instructions,
            match=match)

        if cfg.CONF.METADATA.enable_metrics_proxy:
            match = ofpp.OFPMatch(
                vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
                in_port=metadata_ofport,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=provider_ip,
                ip_proto=in_proto.IPPROTO_TCP,
                tcp_src=cfg.CONF.METADATA.metrics_listen_port)
            actions = [
                ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
                ofpp.OFPActionSetField(eth_dst=dst_mac),
                ofpp.OFPActionSetField(
                    ipv4_src=common_constants.METADATA_DEFAULT_IP),
                ofpp.OFPActionSetField(ipv4_dst=dst_ip),
                ofpp.OFPActionSetField(tcp_src=81),
                ofpp.OFPActionOutput(patch_ofport, 0),
            ]
            instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ]
            self.path_br.install_instructions(
                table_id=p_const.NP_INGRESS_DST_DIRECT,
                priority=203,
                instructions=instructions,
                match=match)

    def ingress_physical_allowed_source_to_int(
            self, provider_vlan, source_cidr):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(
            vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_src=source_cidr)

        instructions = [
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_INGRESS_ALLOWED_DIRECT)
        ]
        self.path_br.install_instructions(
            table_id=p_const.NP_INGRESS_ALLOWED_SOURCES,
            priority=200,
            instructions=instructions,
            match=match)

    def ingress_physical_nic_to_int(
            self, vlan, provider_vlan, provider_ip,
            dst_mac, dst_ip, patch_ofport,
            table=p_const.NP_INGRESS_DST_DIRECT):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(
            vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=provider_ip)

        actions = [
            ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
            ofpp.OFPActionSetField(eth_dst=dst_mac),
            ofpp.OFPActionSetField(ipv4_dst=dst_ip),
            ofpp.OFPActionOutput(patch_ofport, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(
            table_id=table,
            priority=200,
            instructions=instructions,
            match=match)

    def learn_ingress_physical_nic_to_int(self, pvid, local_vlan, eth_src,
                                          mod_eth_src,
                                          ipv4_src, reg_id, port_fixed_ip):
        self._learn_action_for_ingress_physical_nic_to_int(
            pvid, local_vlan, eth_src, mod_eth_src,
            ipv4_src, reg_id, port_fixed_ip)

    def add_flow_snat_br_ingress_direct_to_int_path(
            self, vlan, provider_vlan, provider_ip,
            dst_mac, dst_ip, patch_ofport,
            path):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        if path['protocol'] == 'tcp':
            match = ofpp.OFPMatch(
                vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
                eth_type=ether_types.ETH_TYPE_IP,
                ip_proto=in_proto.IPPROTO_TCP,
                ipv4_dst=provider_ip,
                ipv4_src=path['dest_ip'],
                tcp_src=path['orig_port'])
        elif path['protocol'] == 'udp':
            match = ofpp.OFPMatch(
                vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
                eth_type=ether_types.ETH_TYPE_IP,
                ip_proto=in_proto.IPPROTO_UDP,
                ipv4_dst=provider_ip,
                ipv4_src=path['dest_ip'],
                udp_src=path['orig_port'])

        actions = [
            ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
            ofpp.OFPActionSetField(eth_dst=dst_mac),
            ofpp.OFPActionSetField(
                ipv4_src=path['orig_ip']),
            ofpp.OFPActionSetField(ipv4_dst=dst_ip),
            ofpp.OFPActionOutput(patch_ofport, 0),
        ]
        instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(
            table_id=p_const.NP_INGRESS_DST_DIRECT,
            priority=200,
            instructions=instructions,
            match=match)

    def add_flow_int_br_egress_direct(
            self, in_port, vlan, patch_ofport,
            phy_vlan=None,
            src_ip=None,
            ipv4_dst=common_constants.METADATA_DEFAULT_IP,
            through_sg=False):
        (_dp, ofp, ofpp) = self.int_br._get_dp()

        table = self.get_int_br_direct_output_table(direction='egress',
                                                    through_sg=through_sg)

        if phy_vlan and src_ip:
            match = ofpp.OFPMatch(
                vlan_vid=phy_vlan | ofp.OFPVID_PRESENT,
                in_port=in_port,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_src=src_ip,
                ipv4_dst=ipv4_dst)
            actions = [
                ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
                ofpp.OFPActionOutput(patch_ofport, 0),
            ]
        else:
            match = ofpp.OFPMatch(
                in_port=in_port,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=ipv4_dst)

            firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
            if (through_sg and
                    firewall_driver == 'openvswitch_stateless'):
                actions = [
                    ofpp.OFPActionOutput(patch_ofport, 0),
                ]
            else:
                actions = [
                    ofpp.OFPActionPushVlan(),
                    ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
                    ofpp.OFPActionOutput(patch_ofport, 0),
                ]
        instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.int_br.install_instructions(table_id=table,
                                         priority=200,
                                         instructions=instructions,
                                         match=match)

    def add_flow_int_br_ingress_output(
            self, in_port, vlan, mac, vm_ofport,
            ipv4_src=common_constants.METADATA_DEFAULT_IP,
            through_sg=False):
        (_dp, ofp, ofpp) = self.int_br._get_dp()

        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)

        match = ofpp.OFPMatch(in_port=in_port,
                              eth_type=ether_types.ETH_TYPE_IP,
                              vlan_vid=vlan | ofp.OFPVID_PRESENT,
                              eth_dst=mac,
                              ipv4_src=ipv4_src)

        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if (through_sg and
                firewall_driver in ('openvswitch', 'openvswitch_stateless')):
            instructions = [
                ofpp.OFPInstructionGotoTable(
                    table_id=p_const.DVR_PRE_QOS_TABLE)
            ]
            self.int_br.install_instructions(table_id=p_const.LOCAL_SWITCHING,
                                             priority=200,
                                             instructions=instructions,
                                             match=match)

        actions = [
            ofpp.OFPActionPopVlan(),
            ofpp.OFPActionOutput(vm_ofport, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)
        ]
        self.int_br.install_instructions(table_id=table,
                                         priority=200,
                                         instructions=instructions,
                                         match=match)

    def add_service_datapath_int_br_ingress_output(
            self, in_port, vlan, mac, vm_ofport,
            ipv4_src=common_constants.METADATA_DEFAULT_IP,
            through_sg=False):
        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if not through_sg or firewall_driver != 'openvswitch':
            return

        (_dp, ofp, ofpp) = self.int_br._get_dp()

        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)
        table = min(table, p_const.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE)

        match = ofpp.OFPMatch(in_port=in_port,
                              eth_type=ether_types.ETH_TYPE_IP,
                              reg6=vlan,
                              eth_dst=mac,
                              ipv4_src=ipv4_src)
        actions = [
            ofpp.OFPActionOutput(vm_ofport, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)
        ]
        self.int_br.install_instructions(table_id=table,
                                         priority=200,
                                         instructions=instructions,
                                         match=match)

    def remove_service_datapath_int_br_ingress_output(
            self, in_port, vlan, mac,
            ipv4_src=common_constants.METADATA_DEFAULT_IP,
            through_sg=False):
        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if not through_sg or firewall_driver != 'openvswitch':
            return

        (_dp, ofp, ofpp) = self.int_br._get_dp()
        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)
        table = min(table, p_const.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE)
        self.int_br.uninstall_flows(table_id=table,
                                    in_port=in_port,
                                    eth_type=ether_types.ETH_TYPE_IP,
                                    reg6=vlan,
                                    eth_dst=mac,
                                    ipv4_src=ipv4_src)

    def remove_ports_networking_path_nat_and_arp_flow(
            self, port_vlan, port_mac, port_fixed_ip,
            provider_vlan, provider_ip_addr):
        (_dp, ofp, _ofpp) = self.path_br._get_dp()

        self.path_br.uninstall_flows(
            table_id=p_const.NP_EGRESS_NAT,
            eth_type=ether_types.ETH_TYPE_IP,
            eth_src=port_mac,
            ipv4_src=port_fixed_ip,
            vlan_vid=port_vlan | ofp.OFPVID_PRESENT)
        self.path_br.uninstall_flows(
            table_id=p_const.NP_PROVIDER_IP_ARP_RESPONDER,
            eth_type=ether_types.ETH_TYPE_ARP,
            arp_tpa=provider_ip_addr)
        self.path_br.uninstall_flows(
            table_id=p_const.NP_INGRESS_DST_DIRECT,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=provider_ip_addr,
            vlan_vid=provider_vlan | ofp.OFPVID_PRESENT)
        self.path_br.uninstall_flows(
            table_id=p_const.NP_INGRESS_ALLOWED_DIRECT,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=provider_ip_addr,
            vlan_vid=provider_vlan | ofp.OFPVID_PRESENT)

    def remove_ports_metadata_direct_flow(
            self, ofport, port_vlan, port_mac,
            ofport_int_to_snat,
            phy_vlan=None,
            src_ip=None,
            orig_ip=common_constants.METADATA_DEFAULT_IP):
        self._remove_ports_path_direct_flow(
            ofport, port_vlan, port_mac,
            ofport_int_to_snat, phy_vlan, src_ip, orig_ip)

    def remove_ports_service_path_direct_flow(
            self, ofport, port_vlan, port_mac,
            ofport_int_to_snat,
            phy_vlan=None,
            src_ip=None,
            orig_ip=common_constants.METADATA_DEFAULT_IP,
            through_sg=False):
        table = self.get_int_br_direct_output_table(direction='egress',
                                                    through_sg=through_sg)
        self._remove_ports_path_direct_flow(
            ofport, port_vlan, port_mac,
            ofport_int_to_snat, phy_vlan, src_ip, orig_ip,
            table=table)
        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)
        self._remove_ports_path_direct_flow(
            ofport, port_vlan, port_mac,
            ofport_int_to_snat, phy_vlan, src_ip, orig_ip,
            table=table)

        # remove table=0
        self._remove_ports_path_direct_flow(
            ofport, port_vlan, port_mac,
            ofport_int_to_snat, phy_vlan, src_ip, orig_ip)

    def _remove_ports_path_direct_flow(
            self, ofport, port_vlan, port_mac,
            ofport_int_to_snat,
            phy_vlan=None,
            src_ip=None,
            orig_ip=common_constants.METADATA_DEFAULT_IP,
            table=p_const.LOCAL_SWITCHING):
        (_dp, ofp, _ofpp) = self.int_br._get_dp()

        if phy_vlan and src_ip:
            self.int_br.uninstall_flows(
                table_id=table,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=orig_ip,
                vlan_vid=phy_vlan | ofp.OFPVID_PRESENT,
                ipv4_src=src_ip,
                in_port=ofport)
        else:
            self.int_br.uninstall_flows(
                table_id=table,
                eth_type=ether_types.ETH_TYPE_IP,
                ipv4_dst=orig_ip,
                in_port=ofport)
        self.int_br.uninstall_flows(
            table_id=table,
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_src=orig_ip,
            in_port=ofport_int_to_snat,
            vlan_vid=port_vlan | ofp.OFPVID_PRESENT,
            eth_dst=port_mac)

    def change_dest_mac(self, dest_ip, reg_id, dest_mac):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        table = p_const.NP_EGRESS_DEST_MAC_LEARN
        match = ofpp.OFPMatch(
            eth_type=ether_types.ETH_TYPE_IP,
            reg7=reg_id,
            ipv4_dst=dest_ip)
        actions = [
            ofpp.OFPActionSetField(eth_dst=dest_mac),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_EGRESS_NAT_CLASSIFY)
        ]
        self.path_br.install_instructions(table_id=table,
                                          priority=201,
                                          match=match,
                                          instructions=instructions)

    def egress_service_direct(self, port, ipv4_dst):
        kwargs = {"table_id": p_const.LOCAL_SWITCHING,
                  "priority": 201,
                  "in_port": port,
                  "ipv4_dst": ipv4_dst,
                  "eth_type": ether_types.ETH_TYPE_IP,
                  "dest_table_id": p_const.NP_EGRESS_NAT}
        self.path_br.install_goto(**kwargs)

    def remove_egress_service_direct(self, port, ipv4_dst):
        self.path_br.uninstall_flows(
            table_id=p_const.LOCAL_SWITCHING,
            eth_type=ether_types.ETH_TYPE_IP,
            in_port=port,
            ipv4_dst=ipv4_dst)

    def egress_to_physical_nic(self, path, pvid, nic_ofport):
        (_dp, ofp, ofpp) = self.path_br._get_dp()
        if path['protocol'] == 'tcp':
            table = p_const.NP_EGRESS_TCP
            kwargs = {"eth_type": ether_types.ETH_TYPE_IP,
                      "ip_proto": in_proto.IPPROTO_TCP}
            match = ofpp.OFPMatch(**kwargs)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
            ]
            if nic_ofport != 0:
                actions.append(ofpp.OFPActionOutput(nic_ofport, 0))
            else:
                actions.append(ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0))
        elif path['protocol'] == 'udp':
            table = p_const.NP_EGRESS_UDP
            kwargs = {"eth_type": ether_types.ETH_TYPE_IP,
                      "ip_proto": in_proto.IPPROTO_UDP}
            match = ofpp.OFPMatch(**kwargs)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
            ]
            if nic_ofport != 0:
                actions.append(ofpp.OFPActionOutput(nic_ofport, 0))
            else:
                actions.append(ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0))
        elif path['protocol'] == 'icmp':
            table = p_const.NP_EGRESS_ICMP
            kwargs = {"eth_type": ether_types.ETH_TYPE_IP,
                      "ip_proto": in_proto.IPPROTO_ICMP}
            match = ofpp.OFPMatch(**kwargs)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
            ]
            if nic_ofport != 0:
                actions.append(ofpp.OFPActionOutput(nic_ofport, 0))
            else:
                actions.append(ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0))
        else:
            return
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(table_id=table,
                                          priority=201,
                                          match=match,
                                          instructions=instructions)

    def path_classify_service_path(self):
        (_dp, _ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ip_proto=in_proto.IPPROTO_TCP)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_TCP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ip_proto=in_proto.IPPROTO_UDP)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_UDP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

    def path_destination_icmp_classify(self, dest_ip):
        (_dp, _ofp, ofpp) = self.path_br._get_dp()
        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ip_proto=in_proto.IPPROTO_ICMP,
                              icmpv4_type=icmpv4.ICMP_ECHO_REQUEST,
                              ipv4_dst=dest_ip)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_ICMP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                              ip_proto=in_proto.IPPROTO_ICMP,
                              icmpv4_type=icmpv4.ICMP_ECHO_REPLY,
                              ipv4_dst=dest_ip)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_ICMP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

    def remove_path_destination_icmp_classify(self, dest_ip):
        self.path_br.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IP,
            ip_proto=in_proto.IPPROTO_ICMP,
            icmpv4_type=icmpv4.ICMP_ECHO_REQUEST,
            ipv4_dst=dest_ip)
        self.path_br.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IP,
            ip_proto=in_proto.IPPROTO_ICMP,
            icmpv4_type=icmpv4.ICMP_ECHO_REPLY,
            ipv4_dst=dest_ip)

    def install_allowed_snat_flows(self, pvid, local_vlan, eth_src,
                                   mod_eth_src,
                                   ipv4_src, reg_id):
        for src_cidr in cfg.CONF.SERVICEPATH.allowed_pod_cidrs:
            self._learn_action_for_ingress_physical_nic_to_int(
                pvid, local_vlan, eth_src, mod_eth_src,
                ipv4_src, reg_id, src_cidr)

    def _learn_action_for_ingress_physical_nic_to_int(
            self, pvid, local_vlan, eth_src,
            mod_eth_src,
            ipv4_src, reg_id, src_cidr):
        _dp, ofp, ofpp = self.path_br._get_dp()
        ipv4_src_nxm = int(netaddr.IPAddress(ipv4_src))

        for proto in [in_proto.IPPROTO_TCP, in_proto.IPPROTO_UDP,
                      in_proto.IPPROTO_ICMP]:
            match = ofpp.OFPMatch(
                vlan_vid=local_vlan | ofp.OFPVID_PRESENT,
                eth_src=eth_src,
                eth_type=ether_types.ETH_TYPE_IP,
                ip_proto=proto,
                ipv4_src=src_cidr)
            flow_specs = [
                # Match
                ofpp.NXFlowSpecMatch(src=pvid,
                                     dst=('vlan_tci', 0),
                                     n_bits=12),
                ofpp.NXFlowSpecMatch(src=ether_types.ETH_TYPE_IP,
                                     dst=('eth_type_nxm', 0),
                                     n_bits=16),
                ofpp.NXFlowSpecMatch(src=proto,
                                     dst=('ip_proto_nxm', 0),
                                     n_bits=8),
                ofpp.NXFlowSpecMatch(src=('ipv4_dst_nxm', 0),
                                     dst=('ipv4_src_nxm', 0),
                                     n_bits=32),
                ofpp.NXFlowSpecMatch(src=ipv4_src_nxm,
                                     dst=('ipv4_dst_nxm', 0),
                                     n_bits=32),
                # Action
                ofpp.NXFlowSpecLoad(src=('vlan_tci', 0),
                                    dst=('vlan_tci', 0),
                                    n_bits=16),
                ofpp.NXFlowSpecLoad(src=('eth_src_nxm', 0),
                                    dst=('eth_dst_nxm', 0),
                                    n_bits=48),
                ofpp.NXFlowSpecLoad(src=('ipv4_src_nxm', 0),
                                    dst=('ipv4_dst_nxm', 0),
                                    n_bits=32),
                ofpp.NXFlowSpecOutput(src=('in_port', 0),
                                      dst='',
                                      n_bits=32),
            ]

            if proto == in_proto.IPPROTO_TCP:
                flow_specs.append(
                    ofpp.NXFlowSpecMatch(src=('tcp_dst_nxm', 0),
                                         dst=('tcp_src_nxm', 0),
                                         n_bits=16))
                flow_specs.append(
                    ofpp.NXFlowSpecMatch(src=('tcp_src_nxm', 0),
                                         dst=('tcp_dst_nxm', 0),
                                         n_bits=16))
            elif proto == in_proto.IPPROTO_UDP:
                flow_specs.append(
                    ofpp.NXFlowSpecMatch(src=('udp_dst_nxm', 0),
                                         dst=('udp_src_nxm', 0),
                                         n_bits=16))
                flow_specs.append(
                    ofpp.NXFlowSpecMatch(src=('udp_src_nxm', 0),
                                         dst=('udp_dst_nxm', 0),
                                         n_bits=16))

            actions = [
                ofpp.NXActionLearn(
                    table_id=p_const.NP_INGRESS_DST_DIRECT,
                    cookie=self.path_br.default_cookie,
                    priority=210,
                    hard_timeout=300,
                    specs=flow_specs),
                ofpp.OFPActionPopVlan(),
                ofpp.OFPActionSetField(eth_src=mod_eth_src),
                ofpp.OFPActionSetField(ipv4_src=ipv4_src),
                ofpp.OFPActionSetField(reg7=reg_id),
            ]
            instructions = [
                ofpp.OFPInstructionActions(
                    ofp.OFPIT_APPLY_ACTIONS, actions),
                ofpp.OFPInstructionGotoTable(
                    table_id=p_const.NP_EGRESS_DEST_MAC_LEARN)
            ]
            self.path_br.install_instructions(
                table_id=p_const.NP_EGRESS_NAT,
                priority=200,
                match=match,
                instructions=instructions)

    def remove_learnt_mac_change_flows(self, dest_ip):
        (_dp, _ofp, ofpp) = self.path_br._get_dp()

        table = p_const.NP_EGRESS_DEST_MAC_LEARN
        match = ofpp.OFPMatch(
            eth_type=ether_types.ETH_TYPE_IP,
            ipv4_dst=dest_ip)
        self.path_br.uninstall_flows(table_id=table,
                                     match=match)

    def remove_allowed_snat_flows(self, local_vlan, eth_src):
        _dp, ofp, _ofpp = self.path_br._get_dp()

        for src_cidr in cfg.CONF.SERVICEPATH.allowed_pod_cidrs:
            for proto in [in_proto.IPPROTO_TCP, in_proto.IPPROTO_UDP,
                          in_proto.IPPROTO_ICMP]:
                self.path_br.uninstall_flows(
                    table_id=p_const.NP_EGRESS_NAT,
                    vlan_vid=local_vlan | ofp.OFPVID_PRESENT,
                    eth_src=eth_src,
                    eth_type=ether_types.ETH_TYPE_IP,
                    ip_proto=proto,
                    ipv4_src=src_cidr)

    def get_int_br_direct_output_table(self, direction, through_sg):
        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if (through_sg and
                firewall_driver in ('openvswitch', 'openvswitch_stateless')):
            if direction == 'egress':
                table = p_const.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE
            else:
                if cfg.CONF.AGENT.explicitly_egress_direct:
                    table = p_const.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE
                elif cfg.CONF.AGENT.across_sg_normal:
                    table = p_const.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE
                else:
                    table = p_const.RULES_INGRESS_TABLE
        else:
            table = p_const.LOCAL_SWITCHING

        return table

    def add_flow_int_br_egress_direct_ip6(
            self, in_port, vlan, patch_ofport, ipv6_dst,
            through_sg=False):
        (_dp, ofp, ofpp) = self.int_br._get_dp()

        table = self.get_int_br_direct_output_table(direction='egress',
                                                    through_sg=through_sg)

        match = ofpp.OFPMatch(
                in_port=in_port,
                eth_type=ether_types.ETH_TYPE_IPV6,
                ipv6_dst=ipv6_dst)

        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if (through_sg and
                firewall_driver == 'openvswitch_stateless'):
            actions = [
                ofpp.OFPActionOutput(patch_ofport, 0),
            ]
        else:
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
                ofpp.OFPActionOutput(patch_ofport, 0),
            ]
        instructions = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.int_br.install_instructions(table_id=table,
                                         priority=200,
                                         instructions=instructions,
                                         match=match)

    def add_flow_int_br_ingress_output_ip6(
            self, in_port, vlan, mac, vm_ofport, ipv6_src,
            through_sg=False):
        (_dp, ofp, ofpp) = self.int_br._get_dp()

        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)

        match = ofpp.OFPMatch(in_port=in_port,
                              eth_type=ether_types.ETH_TYPE_IPV6,
                              vlan_vid=vlan | ofp.OFPVID_PRESENT,
                              eth_dst=mac,
                              ipv6_src=ipv6_src)

        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if (through_sg and
                firewall_driver in ('openvswitch', 'openvswitch_stateless')):
            instructions = [
                ofpp.OFPInstructionGotoTable(
                    table_id=p_const.DVR_PRE_QOS_TABLE)
            ]
            self.int_br.install_instructions(table_id=p_const.LOCAL_SWITCHING,
                                             priority=200,
                                             instructions=instructions,
                                             match=match)

        actions = [
            ofpp.OFPActionPopVlan(),
            ofpp.OFPActionOutput(vm_ofport, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)
        ]
        self.int_br.install_instructions(table_id=table,
                                         priority=200,
                                         instructions=instructions,
                                         match=match)

    def add_service_datapath_int_br_ingress_output_ip6(
            self, in_port, vlan, mac, vm_ofport, ipv6_src, through_sg=False):
        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if not through_sg or firewall_driver != 'openvswitch':
            return

        (_dp, ofp, ofpp) = self.int_br._get_dp()

        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)
        table = min(table, p_const.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE)

        match = ofpp.OFPMatch(in_port=in_port,
                              eth_type=ether_types.ETH_TYPE_IPV6,
                              reg6=vlan,
                              eth_dst=mac,
                              ipv6_src=ipv6_src)
        actions = [
            ofpp.OFPActionOutput(vm_ofport, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)
        ]
        self.int_br.install_instructions(table_id=table,
                                         priority=200,
                                         instructions=instructions,
                                         match=match)

    def egress_service_direct_ip6(self, port, ipv6_dst):
        kwargs = {"table_id": p_const.LOCAL_SWITCHING,
                  "priority": 201,
                  "in_port": port,
                  "ipv6_dst": ipv6_dst,
                  "eth_type": ether_types.ETH_TYPE_IPV6,
                  "dest_table_id": p_const.NP_EGRESS_NAT}
        self.path_br.install_goto(**kwargs)

    def add_flow_snat_br_src_addr_check_ip6(self, vlan, src_mac, src_ip,
                                            provider_mac):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(vlan_vid=vlan | ofp.OFPVID_PRESENT,
                              eth_type=ether_types.ETH_TYPE_IPV6,
                              eth_src=src_mac,
                              ipv6_src=src_ip)
        actions = [
            ofpp.OFPActionPopVlan(),
            ofpp.OFPActionSetField(eth_src=provider_mac),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_EGRESS_DEST_MAC_LEARN)
        ]
        self.path_br.install_instructions(table_id=p_const.NP_EGRESS_NAT,
                                          priority=201,
                                          instructions=instructions,
                                          match=match)

    def ndp_op_to_controller_ip6(self, vlan_id, ipv6_dst,
                                 icmpv6_type=constants.ICMPV6_TYPE_NA):
        (_dp, ofp, ofpp) = self.path_br._get_dp()
        match = ofpp.OFPMatch(vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
                              eth_type=ether_types.ETH_TYPE_IPV6,
                              ip_proto=in_proto.IPPROTO_ICMPV6,
                              icmpv6_type=icmpv6_type,
                              ipv6_nd_target=ipv6_dst)
        actions = [
            ofpp.OFPActionOutput(ofp.OFPP_CONTROLLER, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(table_id=p_const.LOCAL_SWITCHING,
                                          priority=202,
                                          instructions=instructions,
                                          match=match)

    def change_dest_mac_ip6(self, dest_ip, dest_mac):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        table = p_const.NP_EGRESS_DEST_MAC_LEARN
        match = ofpp.OFPMatch(
            eth_type=ether_types.ETH_TYPE_IPV6,
            ipv6_dst=dest_ip)
        actions = [
            ofpp.OFPActionSetField(eth_dst=dest_mac),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_EGRESS_NAT_CLASSIFY)
        ]
        self.path_br.install_instructions(table_id=table,
                                          priority=201,
                                          match=match,
                                          instructions=instructions)

    def install_ndp_responder(self, vlan_id, dest_ip, mac):
        # TODO(liuyulong): use native method
        self.path_br.add_flow(
            table=p_const.LOCAL_SWITCHING,
            priority=205,
            dl_vlan=vlan_id,
            dl_type=common_constants.ETHERTYPE_IPV6,
            nw_proto=constants.PROTO_NUM_IPV6_ICMP,
            icmp_type=icmpv6.ND_NEIGHBOR_SOLICIT,
            nd_target=dest_ip,
            actions="set_field:%s->icmpv6_type,resubmit(,%s)" % (
                icmpv6.ND_NEIGHBOR_ADVERT,
                p_const.NP_PROVIDER_IP_ARP_RESPONDER))

        # TODO(liuyulong): use native method
        try:
            self.path_br.add_flow(
                table=p_const.NP_PROVIDER_IP_ARP_RESPONDER,
                priority=201,
                dl_vlan=vlan_id,
                dl_type=common_constants.ETHERTYPE_IPV6,
                nw_proto=constants.PROTO_NUM_IPV6_ICMP,
                icmp_type=icmpv6.ND_NEIGHBOR_ADVERT,
                nd_target=dest_ip,
                actions='move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],'
                        'set_field:%(mac)s->eth_src,'
                        'move:NXM_NX_IPV6_SRC[]->NXM_NX_IPV6_DST[],'
                        'set_field:%(ipv6_src)s->ipv6_src,'
                        'set_field:%(icmpv6_code)s->icmpv6_code,'
                        'set_field:%(nd_reserved)s->nd_reserved,'
                        'set_field:%(mac)s->nd_tll,'
                        'set_field:%(nd_op_type)s->nd_options_type,'
                        'in_port' % {
                            "mac": mac,
                            "ipv6_src": dest_ip,
                            "icmpv6_code": 0,
                            "nd_reserved": "0x60000000",
                            "nd_op_type": icmpv6.ND_OPTION_TLA}
            )
        except Exception as ex:
            LOG.warning("Failed to install ndp responder flow, raise %s", ex)

    def install_ndp_responder1(self, vlan_id, dest_ip, mac):
        # TODO(liuyulong): use this method
        (_dp, ofp, ofpp) = self.path_br._get_dp()
        match = ofpp.OFPMatch(
            vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            ipv6_nd_target=dest_ip,
            icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT)

        actions = [
            ofpp.OFPActionSetField(icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_PROVIDER_IP_ARP_RESPONDER)
        ]
        self.path_br.install_instructions(
            table_id=p_const.LOCAL_SWITCHING,
            priority=205,
            match=match,
            instructions=instructions)

        match = ofpp.OFPMatch(
            vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            ipv6_nd_target=dest_ip,
            icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT)
        actions = [
            ofpp.NXActionRegMove(src_field='eth_src',
                                 dst_field='eth_dst',
                                 n_bits=48),
            ofpp.OFPActionSetField(eth_src=mac),
            ofpp.NXActionRegMove(src_field='ipv6_src',
                                 dst_field='ipv6_dst',
                                 n_bits=128),
            ofpp.OFPActionSetField(ipv6_src=dest_ip),
            ofpp.OFPActionSetField(icmpv6_code=0),
            ofpp.OFPActionSetField(ipv6_nd_target=dest_ip),
            ofpp.OFPActionSetField(ipv6_nd_tll=mac),
            # ryu does not support set icmpv6 NA reserved and option type
            # ofpp.OFPActionSetField(ipv6_nd_reserved=0x60000000),
            # ofpp.OFPActionSetField(ipv6_nd_option_type=icmpv6.ND_OPTION_TLA),
            ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions)
        ]
        self.path_br.install_instructions(
            table_id=p_const.NP_PROVIDER_IP_ARP_RESPONDER,
            priority=201,
            match=match,
            instructions=instructions)

    def remove_ndp_responder(self, vlan_id, dest_ip):
        (_dp, ofp, _ofpp) = self.path_br._get_dp()
        self.path_br.uninstall_flows(
            table_id=p_const.LOCAL_SWITCHING,
            vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            ipv6_nd_target=dest_ip,
            icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT)

        self.path_br.uninstall_flows(
            table_id=p_const.NP_PROVIDER_IP_ARP_RESPONDER,
            vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            ipv6_nd_target=dest_ip,
            icmpv6_type=icmpv6.ND_NEIGHBOR_ADVERT)

    def ingress_physical_nic_to_int_ip6(
            self, vlan, provider_vlan, dest_ip,
            dst_mac, patch_ofport):
        (_dp, ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(
            vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ipv6_dst=dest_ip)

        actions = [
            ofpp.OFPActionSetField(vlan_vid=vlan | ofp.OFPVID_PRESENT),
            ofpp.OFPActionSetField(eth_dst=dst_mac),
            ofpp.OFPActionOutput(patch_ofport, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(
            table_id=p_const.NP_INGRESS_DST_DIRECT,
            priority=200,
            instructions=instructions,
            match=match)

    def ndp_direct_ip6(self, vlan_id):
        (_dp, ofp, ofpp) = self.path_br._get_dp()
        match = ofpp.OFPMatch(
            vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT)

        instructions = [
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_PROVIDER_IP_ARP_RESPONDER)
        ]

        self.path_br.install_instructions(table_id=p_const.LOCAL_SWITCHING,
                                          priority=201,
                                          instructions=instructions,
                                          match=match)

    def ingress_direct_ip6(self, vlan_id):
        (_dp, ofp, ofpp) = self.path_br._get_dp()
        match = ofpp.OFPMatch(vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
                              eth_type=ether_types.ETH_TYPE_IPV6)

        instructions = [
            ofpp.OFPInstructionGotoTable(
                table_id=p_const.NP_INGRESS_DST_DIRECT)
        ]
        self.path_br.install_instructions(table_id=p_const.LOCAL_SWITCHING,
                                          priority=201,
                                          instructions=instructions,
                                          match=match)

    def path_classify_service_path_ip6(self):
        (_dp, _ofp, ofpp) = self.path_br._get_dp()

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                              ip_proto=in_proto.IPPROTO_TCP)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_TCP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                              ip_proto=in_proto.IPPROTO_UDP)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_UDP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

    def path_destination_icmp_classify_ip6(self, dest_ipv6):
        (_dp, _ofp, ofpp) = self.path_br._get_dp()
        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                              ip_proto=in_proto.IPPROTO_ICMPV6,
                              icmpv6_type=icmpv6.ICMPV6_ECHO_REQUEST,
                              ipv6_dst=dest_ipv6)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_ICMP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                              ip_proto=in_proto.IPPROTO_ICMPV6,
                              icmpv6_type=icmpv6.ICMPV6_ECHO_REPLY,
                              ipv6_dst=dest_ipv6)
        instructions = [ofpp.OFPInstructionGotoTable(
            table_id=p_const.NP_EGRESS_ICMP)]
        self.path_br.install_instructions(
            table_id=p_const.NP_EGRESS_NAT_CLASSIFY,
            priority=201,
            instructions=instructions,
            match=match)

    def remove_path_destination_icmp_classify_ip6(self, dest_ipv6):
        self.path_br.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            icmpv6_type=icmpv6.ICMPV6_ECHO_REQUEST,
            ipv6_dst=dest_ipv6)
        self.path_br.uninstall_flows(
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            icmpv6_type=icmpv6.ICMPV6_ECHO_REPLY,
            ipv6_dst=dest_ipv6)

    def egress_to_physical_nic_ip6(self, path, pvid, nic_ofport):
        (_dp, ofp, ofpp) = self.path_br._get_dp()
        if path['protocol'] == 'tcp':
            table = p_const.NP_EGRESS_TCP
            kwargs = {"eth_type": ether_types.ETH_TYPE_IPV6,
                      "ip_proto": in_proto.IPPROTO_TCP}
            match = ofpp.OFPMatch(**kwargs)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
            ]
            if nic_ofport != 0:
                actions.append(ofpp.OFPActionOutput(nic_ofport, 0))
            else:
                actions.append(ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0))
        elif path['protocol'] == 'udp':
            table = p_const.NP_EGRESS_UDP
            kwargs = {"eth_type": ether_types.ETH_TYPE_IPV6,
                      "ip_proto": in_proto.IPPROTO_UDP}
            match = ofpp.OFPMatch(**kwargs)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
            ]
            if nic_ofport != 0:
                actions.append(ofpp.OFPActionOutput(nic_ofport, 0))
            else:
                actions.append(ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0))
        elif path['protocol'] == 'icmp':
            table = p_const.NP_EGRESS_ICMP
            kwargs = {"eth_type": ether_types.ETH_TYPE_IPV6,
                      "ip_proto": in_proto.IPPROTO_ICMPV6}
            match = ofpp.OFPMatch(**kwargs)
            actions = [
                ofpp.OFPActionPushVlan(),
                ofpp.OFPActionSetField(vlan_vid=pvid | ofp.OFPVID_PRESENT),
            ]
            if nic_ofport != 0:
                actions.append(ofpp.OFPActionOutput(nic_ofport, 0))
            else:
                actions.append(ofpp.OFPActionOutput(ofp.OFPP_NORMAL, 0))
        else:
            return
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.path_br.install_instructions(table_id=table,
                                          priority=201,
                                          match=match,
                                          instructions=instructions)

    def remove_flow_int_br_egress_direct_ip6(
            self, in_port, ipv6_dst, through_sg=False):
        table = self.get_int_br_direct_output_table(direction='egress',
                                                    through_sg=through_sg)
        self.int_br.uninstall_flows(
            table_id=table,
            in_port=in_port,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ipv6_dst=ipv6_dst)

    def remove_flow_int_br_ingress_output_ip6(
            self, in_port, vlan, mac, ipv6_src, through_sg=False):
        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)
        (_dp, ofp, _ofpp) = self.int_br._get_dp()
        self.int_br.uninstall_flows(
            table_id=table,
            in_port=in_port,
            eth_type=ether_types.ETH_TYPE_IPV6,
            vlan_vid=vlan | ofp.OFPVID_PRESENT,
            eth_dst=mac,
            ipv6_src=ipv6_src)

        # remove table=0 direct flows
        self.int_br.uninstall_flows(
            table_id=p_const.LOCAL_SWITCHING,
            in_port=in_port,
            eth_type=ether_types.ETH_TYPE_IPV6,
            vlan_vid=vlan | ofp.OFPVID_PRESENT,
            eth_dst=mac,
            ipv6_src=ipv6_src)

    def remove_service_datapath_int_br_ingress_output_ip6(
            self, in_port, vlan, mac, ipv6_src, through_sg=False):
        firewall_driver = cfg.CONF.SECURITYGROUP.firewall_driver or 'noop'
        if not through_sg or firewall_driver != 'openvswitch':
            return

        (_dp, ofp, ofpp) = self.int_br._get_dp()
        table = self.get_int_br_direct_output_table(direction='ingress',
                                                    through_sg=through_sg)
        table = min(table, p_const.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE)
        self.int_br.uninstall_flows(table_id=table,
                                    in_port=in_port,
                                    eth_type=ether_types.ETH_TYPE_IPV6,
                                    reg6=vlan,
                                    eth_dst=mac,
                                    ipv6_src=ipv6_src)

    def remove_egress_service_direct_ip6(self, port, ipv6_dst):
        self.path_br.uninstall_flows(
            table_id=p_const.LOCAL_SWITCHING,
            eth_type=ether_types.ETH_TYPE_IPV6,
            in_port=port,
            ipv6_dst=ipv6_dst)

    def remove_flow_snat_br_src_addr_check_ip6(
            self, vlan, src_mac, src_ip):
        (_dp, ofp, _ofpp) = self.path_br._get_dp()
        self.path_br.uninstall_flows(
            table_id=p_const.NP_EGRESS_NAT,
            vlan_vid=vlan | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            eth_src=src_mac,
            ipv6_src=src_ip)

    def remove_ndp_op_to_controller_ip6(
            self, vlan_id, ipv6_dst,
            icmpv6_type=constants.ICMPV6_TYPE_NA):
        (_dp, ofp, _ofpp) = self.path_br._get_dp()
        self.path_br.uninstall_flows(
            table_id=p_const.LOCAL_SWITCHING,
            vlan_vid=vlan_id | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            icmpv6_type=icmpv6_type,
            ipv6_nd_target=ipv6_dst)

    def remove_ingress_physical_nic_to_int_ip6(
            self, provider_vlan, dest_ip):
        (_dp, ofp, _ofpp) = self.path_br._get_dp()

        self.path_br.uninstall_flows(
            table_id=p_const.NP_INGRESS_DST_DIRECT,
            vlan_vid=provider_vlan | ofp.OFPVID_PRESENT,
            eth_type=ether_types.ETH_TYPE_IPV6,
            ipv6_dst=dest_ip)

    def remove_learnt_mac_change_flows_ip6(self, ipv6_dst):
        (_dp, _ofp, ofpp) = self.path_br._get_dp()

        table = p_const.NP_EGRESS_DEST_MAC_LEARN
        match = ofpp.OFPMatch(
            eth_type=ether_types.ETH_TYPE_IPV6,
            ipv6_dst=ipv6_dst)
        self.path_br.uninstall_flows(table_id=table,
                                     match=match)
