# Copyright (c) 2024 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

from netaddr import EUI
from neutron_lib import context
from oslo_config import cfg

from neutron.agent.common import ovs_lib
from neutron.agent.l2.extensions.traffic_mirror import extension as tm_ext
from neutron.api.rpc.callbacks.consumer import registry
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import resources_rpc
from neutron.conf.plugins.ml2.drivers import ovs_conf
import neutron.objects.ports as port_obj
import neutron.objects.traffic_mirror as tm_obj
from neutron.plugins.ml2.drivers.openvswitch.agent \
    import ovs_agent_extension_api as ovs_ext_api
from neutron.tests import base


class TrafficMirrorMockObject(object):
    def set_env(self,
                tm_extension,
                allowed_address_mac=None,
                port_security_enabled=True,
                port_binding_network='vxlan'):
        self._set_env(allowed_address_mac, port_security_enabled,
                      port_binding_network)
        self.tm_ext = tm_extension
        self.tm_ext.br_int.get_vif_port_by_id = self.get_vif_port_by_id
        self.tm_ext.get_port_by_id = self.get_port_by_id
        self.tm_ext.get_port_local_tag = self.get_port_local_tag
        self.tm_ext.get_filter_rules = self.get_filter_rules
        self.tm_ext.pfn_subnets = set()
        self.tm_ext.host_tunnel_ip_dict = dict()
        self.tm_ext.create_tunnel_port = self.create_tunnel_port
        self.tm_ext.delete_tunnel_port = self.delete_tunnel_port
        self.tm_ext.br_mirror.get_port_ofport = self.br_mirror_get_port_ofport
        self.tm_ext.br_int.get_port_ofport = self.br_int_get_port_ofport
        self.tm_ext.get_host_tunnel_ip = self.get_host_tunnel_ip
        self.tm_ext.support_add_vxlan = self.support_add_vxlan
        self.tm_ext.source_port_id = self.source_port_id
        self.tm_ext.source_vif_port = self.source_vif_port
        self.tm_ext.source_port_dict = self.source_port_dict
        self.tm_ext.target_vif_port = self.target_vif_port
        self.tm_ext.filter_id = self.filter_id
        self.tm_ext.source_port_vlan = self.source_local_vlan
        return (self.br_int_patch_ofport, self.source_local_vlan,
                self.source_mac_addr, self.source_ofport,
                self.target_local_vlan, self.target_mac_addr, self.tm_session,
                self.tm_session_vni, self.tun_patch_ofport, self.tunnel_ofport)

    def _set_env(self,
                 allowed_address_mac=None,
                 port_security_enabled=True,
                 port_binding_network='vxlan'):
        self.source_port_id = '60044de9-13fa-4a06-b0b3-67d1f36eb8d2'
        self.source_mac_addr = 'fa:16:3e:df:b7:49'
        self.source_port_obj = port_obj.Port(
            id=self.source_port_id,
            project_id='db378f60a36e4038a7995af25685b501',
            name='source_port',
            network_id='9560f424-0510-4257-b886-5a531449d3a6',
            mac_address=EUI(self.source_mac_addr),
            admin_state_up=True,
            status='ACTIVE')
        self.source_port_dict = self.source_port_obj.to_dict()
        self.source_port_dict['mac_address'] = self.source_mac_addr
        self.source_port_dict['bindings'] = [{'host': 'ecm0001'}]
        if allowed_address_mac:
            self.source_port_dict['allowed_address_pairs'] = [{
                'mac_address':
                allowed_address_mac,
                'ip_address':
                '**********21'
            }]
        else:
            self.source_port_dict['allowed_address_pairs'] = []
        if port_security_enabled:
            self.source_port_dict['security'] = {'port_security_enabled': True}
        else:
            self.source_port_dict['security'] = {
                'port_security_enabled': False
            }
        self.source_port_dict['binding_levels'] = [{
            'host': 'ecm0001',
            'driver': 'openvswitch',
            'segment': {
                'network_type': port_binding_network,
                'physical_network': 'default',
            }
        }]
        self.source_port_dict['fixed_ips'] = [{
            'subnet_id': 'd8bdd4e0-50a0-433b-afaf-d2d59632ae62',
            'ip_address': '************'
        }, {
            'subnet_id': '3dfd1cc2-b65b-41e7-bcb5-2fb065f2e006',
            'ip_address': '10:10:10:10::120'
        }]

        self.target_port_id = '2de740a5-795d-4474-b946-10154ba2db02'
        self.target_mac_addr = 'fa:16:3e:83:70:8e'
        self.target_port_obj = port_obj.Port(
            id=self.target_port_id,
            project_id='db378f60a36e4038a7995af25685b501',
            name='target_port',
            network_id='9560f424-0510-4257-b886-5a531449d3a6',
            mac_address=EUI(self.target_mac_addr),
            admin_state_up=True,
            status='ACTIVE')
        self.target_port_dict = self.target_port_obj.to_dict()
        self.target_port_dict['bindings'] = [{'host': 'ecm0002'}]
        self.target_port_dict['mac_address'] = self.target_mac_addr
        self.target_port_dict['fixed_ips'] = [{
            'subnet_id': 'd8bdd4e0-50a0-433b-afaf-d2d59632ae62',
            'ip_address': '***********'
        }]

        self.tm_session_bind = tm_obj.TrafficMirrorSourceBinding(
            source_port_id=self.source_port_id,
            traffic_mirror_session_id="f747e3b2-7722-44c9-a162-54c2e1384938",
        )
        self.tm_session_vni = 30000
        self.filter_id = "0c8c94c1-7e0a-44c3-9d59-980a4ad45756"
        self.tm_session = tm_obj.TrafficMirrorSession(
            id="f747e3b2-7722-44c9-a162-54c2e1384938",
            project_id="db378f60a36e4038a7995af25685b501",
            name="tm_session_1",
            traffic_mirror_filter_id=self.filter_id,
            traffic_mirror_target_port_id=self.target_port_id,
            traffic_mirror_target_type="eni",
            virtual_network_id=self.tm_session_vni,
            packet_length=120,
            traffic_mirror_sources=[self.tm_session_bind],
            priority=50,
            segmentation_id=10000,
            enabled=True)

        self.source_ofport = 22
        self.source_local_vlan = 3
        self.source_vif_port = ovs_lib.VifPort('source_port',
                                               self.source_ofport,
                                               self.source_port_id,
                                               self.source_mac_addr, "br-int")
        self.target_ofport = 123
        self.target_local_vlan = 2
        self.target_vif_port = ovs_lib.VifPort('target_port',
                                               self.target_ofport,
                                               self.target_port_id,
                                               self.target_mac_addr, 'br-int')

        self.tunnel_ofport = 30
        self.br_int_patch_ofport = 40
        self.tun_patch_ofport = 100
        self.int_patch_ofport = 110

    def get_vif_port_by_id(self, port_id):
        if port_id == self.source_port_id:
            return self.source_vif_port
        elif port_id == self.target_port_id:
            return self.target_vif_port

    def get_port_by_id(self, port_id):
        if port_id == self.source_port_id:
            return self.source_port_dict
        elif port_id == self.target_port_id:
            return self.target_port_dict

    def get_port_local_tag(self, port_id):
        if port_id == self.source_port_id:
            return self.source_local_vlan
        elif port_id == self.target_port_id:
            return self.target_local_vlan

    def get_filter_rules(self, filter_id=None):
        icmp_egress_rule = {
            'direction': 'egress',
            'protocol': 'icmp',
            'ethertype': 'IPv4',
            'priority': 10,
            'action': 'accept',
            'src_cidr': '*************/24'
        }
        icmp_ingress_rule = {
            'direction': 'ingress',
            'protocol': 'icmp',
            'ethertype': 'IPv4',
            'priority': 10,
            'action': 'reject',
            'src_cidr': '*************/24'
        }

        tcp_egress_rule = {
            'direction': 'egress',
            'protocol': 'tcp',
            'ethertype': 'IPv4',
            'priority': 10,
            'action': 'accept',
            'dst_port_range_max': 20,
            'dst_port_range_min': 20,
            'src_cidr': '*************/24'
        }
        tcp_ingress_rule = {
            'direction': 'ingress',
            'protocol': 'tcp',
            'ethertype': 'IPv4',
            'priority': 20,
            'action': 'accept',
            'dst_port_range_max': 100,
            'dst_port_range_min': 20,
            'src_cidr': '*************/24'
        }
        v6_icmp_ingress_rule = {
            'direction': 'ingress',
            'protocol': 'icmp',
            'ethertype': 'IPv6',
            'priority': 70,
            'action': 'reject',
            'src_cidr': '192:192:193::0/120'
        }
        v6_udp_egress_rule = {
            'direction': 'egress',
            'protocol': 'tcp',
            'ethertype': 'IPv6',
            'priority': 50,
            'action': 'accept',
            'dst_port_range_max': 100,
            'dst_port_range_min': 20,
            'dst_cidr': '192:192:192::0/64'
        }
        v6_tcp_ingress_rule = {
            'direction': 'ingress',
            'protocol': 'tcp',
            'ethertype': 'IPv6',
            'priority': 70,
            'action': 'accept',
            'dst_port_range_max': 22,
            'dst_port_range_min': 22,
            'src_cidr': '192:192:193::0/120'
        }
        rule_list = [
            icmp_egress_rule, icmp_ingress_rule, tcp_egress_rule,
            tcp_ingress_rule, v6_icmp_ingress_rule, v6_udp_egress_rule,
            v6_tcp_ingress_rule
        ]
        filter_rules = {
            'rules': rule_list,
            'id': 'dc6809af-da2d-4d53-b969-1ca001f67ffb',
        }
        if filter_id == self.filter_id:
            return filter_rules
        else:
            return dict()

    def create_tunnel_port(self, remote_ip, virtual_net):
        if remote_ip and virtual_net:
            return self.tunnel_ofport

    def delete_tunnel_port(self, remote_ip, virtual_net):
        if remote_ip and virtual_net:
            return True

    def br_mirror_get_port_ofport(self, port_id):
        if port_id:
            return self.br_int_patch_ofport

    def br_int_get_port_ofport(self, port_name):
        if port_name == 'patch-tun':
            return self.tun_patch_ofport
        if port_name == 'int-br-provider':
            return self.int_patch_ofport

    def get_host_tunnel_ip(self, host):
        if host == 'ecm0001':
            return '**********'
        elif host == 'ecm0002':
            return '**********'

    def support_add_vxlan(self):
        return True


class TrafficMirrorExtensionTestCase(base.BaseTestCase):
    def setUp(self):
        super(TrafficMirrorExtensionTestCase, self).setUp()
        ovs_conf.register_ovs_agent_opts(cfg=cfg.CONF)
        self.enable_traffic_mirror = True
        self.context = context.get_admin_context()
        self.int_br = mock.Mock()
        self.br_mirror = mock.Mock()
        self.plugin_rpc = mock.Mock()
        self.remote_resource_cache = mock.Mock()
        self.plugin_rpc.remote_resource_cache = self.remote_resource_cache
        self.tm_ext = tm_ext.TrafficMirrorAgentExtension()
        self._support_add_vxlan = True
        self.bridge_mappings = {'default': 'br-provider'}
        self.agent_api = ovs_ext_api.OVSAgentExtensionAPI(
            self.int_br,
            tun_br=mock.Mock(),
            phys_brs={},
            plugin_rpc=self.plugin_rpc,
            phys_ofports={},
            bridge_mappings=self.bridge_mappings)
        self.tm_ext.phy_bridges = self.agent_api.bridge_mappings
        self._connection = mock.Mock()
        self._connection.create_consumer = mock.Mock()
        self.tm_ext.consume_api(self.agent_api)
        self.tm_ext.cache_api = mock.Mock()
        self.tm_ext._connection = self._connection
        self.tm_ext._handle_notification = mock.Mock()
        self.tm_ext._handle_filter_notification = mock.Mock()
        self.tm_ext.context = mock.Mock()
        self.tm_ext.dump_table_action_features = mock.Mock(
            return_value=['add_vxlan'])
        self.tm_ext.br_mirror = mock.Mock()
        cfg.CONF.set_override('enable_traffic_mirror', True, 'TRAFFIC_MIRROR')
        cfg.CONF.set_override('traffic_peer_patch_port', 'int-br-mirror',
                              'TRAFFIC_MIRROR')
        cfg.CONF.set_override('int_peer_patch_port', 'mirror-br-int',
                              'TRAFFIC_MIRROR')
        cfg.CONF.set_override('carrier_network_type', 'vxlan',
                              'TRAFFIC_MIRROR')
        cfg.CONF.set_override('firewall_driver', 'openvswitch',
                              'SECURITYGROUP')
        self.tm_ext.conf = cfg.CONF.TRAFFIC_MIRROR
        self.tm_ext.br_int = mock.Mock()
        self.tm_ext.host = 'ecm0001'

    @mock.patch.object(registry, 'register')
    @mock.patch.object(resources_rpc, 'ResourcesPushRpcCallback')
    def test_register_rpc_consumers(self, rpc_mock, subscribe_mock):
        self.tm_ext._register_rpc_consumers()
        self.assertEqual(subscribe_mock.call_args_list, [
            mock.call(self.tm_ext._handle_notification,
                      resources.TRAFFIC_MIRROR_SESSION),
            mock.call(self.tm_ext._handle_filter_notification,
                      resources.TRAFFIC_MIRROR_FILTER)
        ])

        topic_session = resources_rpc.resource_type_versioned_topic(
            resources.TRAFFIC_MIRROR_SESSION)
        topic_filter = resources_rpc.resource_type_versioned_topic(
            resources.TRAFFIC_MIRROR_FILTER)

        self.assertEqual(self._connection.create_consumer.call_args_list, [
            mock.call(topic_session, [rpc_mock()], fanout=True),
            mock.call(topic_filter, [rpc_mock()], fanout=True)
        ])
        self.assertEqual(self._connection.consume_in_threads.call_args_list,
                         [mock.call()])

    def test_pfn_subnets(self):
        self.tm_ext.conf = cfg.CONF.TRAFFIC_MIRROR
        self.tm_ext.agent_id = 'ovs-agent-%s' % cfg.CONF.host
        self.agent_api.plugin_rpc = mock.Mock()
        mock_get_privatefloating_info = mock.patch.object(
            self.tm_ext.agent_api.plugin_rpc,
            'get_privatefloating_info',
            return_value={
                'privatefloating_network': {
                    'subnets_detail': [{
                        'id': 'pfn_subnet_uuid'
                    }]
                }
            }).start()
        self.tm_ext.get_pfn_subnets()
        self.assertEqual(mock_get_privatefloating_info.call_args_list, [
            mock.call(self.tm_ext.context,
                      agent_id=self.tm_ext.agent_id,
                      host=cfg.CONF.host)
        ])
        self.assertEqual(self.tm_ext.pfn_subnets, {'pfn_subnet_uuid'})

    @mock.patch.object(resources_rpc, 'ResourcesPullRpcApi')
    @mock.patch('oslo_service.loopingcall.FixedIntervalLoopingCall')
    def test_initialize(self, loop_mock, rpc_mock):
        mock.patch.object(self.tm_ext, '_register_rpc_consumers').start()
        mock.patch.object(self.tm_ext.agent_api, 'request_int_br').start()
        mock.patch.object(self.tm_ext, 'get_pfn_subnets').start()
        mock.patch.object(self.tm_ext, '_init_traffic_bridge').start()
        mock.patch.object(self.tm_ext, '_set_br_protocol').start()
        mock.patch.object(self.tm_ext,
                          '_fetch_sync_all_traffic_mirror_sessions').start()
        self.tm_ext.initialize(None, None)
        self.assertEqual(self.tm_ext.resource_rpc, rpc_mock())
        self.assertEqual(self.tm_ext._register_rpc_consumers.call_args_list,
                         [mock.call()])
        self.assertEqual(self.tm_ext.agent_api.request_int_br.call_args_list,
                         [mock.call()])
        self.assertEqual(loop_mock.call_count, 1)
        self.assertEqual(self.tm_ext.init_task, loop_mock())

    def test_init_extension(self):
        mock.patch.object(
            self.tm_ext, 'get_pfn_subnets').start()
        mock.patch.object(
            self.tm_ext, '_init_traffic_bridge').start()
        mock.patch.object(
            self.tm_ext, '_set_br_protocol').start()
        mock.patch.object(
            self.tm_ext, '_init_base_flows').start()
        mock.patch.object(
            self.tm_ext,
            '_fetch_sync_all_traffic_mirror_sessions').start()
        self.tm_ext.init_task = mock.Mock()
        self.tm_ext.init_extension()
        self.assertEqual(self.tm_ext.get_pfn_subnets.call_args_list,
                         [mock.call()])
        self.assertEqual(self.tm_ext._init_traffic_bridge.call_args_list,
                         [mock.call()])
        self.assertEqual(self.tm_ext._set_br_protocol.call_args_list,
                         [mock.call()])
        self.assertEqual(
            self.tm_ext._fetch_sync_all_traffic_mirror_sessions.call_args_list,
            [mock.call()])

    def test_create_traffic_mirror_session(self):
        (br_int_patch_ofport, source_local_vlan, source_mac_addr,
         source_ofport, target_local_vlan, target_mac_addr, tm_session,
         tm_session_vni, tun_patch_ofport,
         tunnel_ofport) = TrafficMirrorMockObject().set_env(self.tm_ext)
        mock.patch.object(self.tm_ext, 'install_source_port_flow').start()
        mock.patch.object(self.tm_ext,
                          'install_output_flow_from_br_int').start()
        mock.patch.object(self.tm_ext, 'add_br_mirror_output_flow').start()
        mock.patch.object(self.tm_ext,
                          'install_accept_flow_from_br_mirror').start()
        mock.patch.object(self.tm_ext, 'install_accept_flow_to_target').start()
        mock.patch.object(self.tm_ext,
                          'process_source_port_filter_actions').start()

        self.tm_ext.create_traffic_mirror_session(None, tm_session)
        filter_id = "0c8c94c1-7e0a-44c3-9d59-980a4ad45756"
        filter_call_args = mock.call(self.tm_ext.source_port_dict,
                                     self.tm_ext.source_vif_port,
                                     source_local_vlan,
                                     self.tm_ext.get_filter_rules(filter_id))
        self.assertEqual(
            [filter_call_args],
            self.tm_ext.process_source_port_filter_actions.call_args_list)

        source_port_calls = [
            mock.call(direction='ingress',
                      dl_src=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      port_security=True,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      port_security=True,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=tun_patch_ofport,
                      is_phy_ofport=True,
                      port_security=True,
                      priority=60)
        ]
        self.assertEqual(self.tm_ext.install_source_port_flow.call_args_list,
                         source_port_calls)

        output_flow_calls = [
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=tm_session_vni),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=source_mac_addr,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=tm_session_vni),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      mod_dl_dst=target_mac_addr,
                      mod_dl_src=source_mac_addr,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=tm_session_vni)
        ]
        self.assertEqual(
            self.tm_ext.install_output_flow_from_br_int.call_args_list,
            output_flow_calls)

        self.assertEqual(
            self.tm_ext.install_accept_flow_from_br_mirror.call_args_list, [
                mock.call(mod_vlan_id=2, tun_id=10000),
            ])
        accept_flow_calls = [
            mock.call(br_int_patch_ofport,
                      dl_vlan=target_local_vlan,
                      dst_mac=target_mac_addr,
                      target_port=123)
        ]
        self.assertEqual(
            accept_flow_calls,
            self.tm_ext.install_accept_flow_to_target.call_args_list)

    def test_delete_traffic_mirror_session(self):
        (br_int_patch_ofport, source_local_vlan, source_mac_addr,
         source_ofport, target_local_vlan, target_mac_addr, tm_session,
         tm_session_vni, tun_patch_ofport,
         tunnel_ofport) = TrafficMirrorMockObject().set_env(self.tm_ext)
        mock.patch.object(self.tm_ext, 'uninstall_source_port_flow').start()
        mock.patch.object(self.tm_ext, 'uninstall_filter_flows').start()
        mock.patch.object(self.tm_ext,
                          'delete_output_flow_from_br_int').start()
        mock.patch.object(self.tm_ext, '_delete_traffic_flow').start()
        mock.patch.object(self.tm_ext,
                          'uninstall_accept_flow_from_br_mirror').start()
        mock.patch.object(self.tm_ext,
                          'uninstall_accept_flow_to_target').start()
        mock.patch.object(self.tm_ext, 'delete_tunnel_port').start()

        self.tm_ext.delete_traffic_mirror_session(None, tm_session)
        self.assertEqual(
            self.tm_ext.uninstall_source_port_flow.call_args_list, [
                mock.call(direction='ingress',
                          dl_src=source_mac_addr,
                          in_port=source_ofport),
                mock.call(direction='egress', dl_dst=source_mac_addr)
            ])
        self.assertEqual(self.tm_ext.uninstall_filter_flows.call_args_list, [
            mock.call(direction='ingress',
                      in_port=source_ofport,
                      mac_addr=source_mac_addr),
            mock.call(direction='egress',
                      dl_vlan=source_local_vlan,
                      mac_addr=source_mac_addr)
        ])
        self.assertEqual(
            self.tm_ext.delete_output_flow_from_br_int.call_args_list, [
                mock.call(dl_src=source_mac_addr, in_port=source_ofport),
                mock.call(dl_dst=source_mac_addr)
            ])
        self.assertEqual(self.tm_ext._delete_traffic_flow.call_args_list, [
            mock.call(dl_src=source_mac_addr, table=0),
            mock.call(dl_dst=source_mac_addr, table=0)
        ])
        self.assertEqual(
            [mock.call(tun_id=10000)],
            self.tm_ext.uninstall_accept_flow_from_br_mirror.call_args_list)
        self.assertEqual(
            [mock.call(br_int_patch_ofport, dst_mac=target_mac_addr)],
            self.tm_ext.uninstall_accept_flow_to_target.call_args_list)
        self.tm_ext.delete_tunnel_port.assert_called_with('**********', 10000)

    def test_create_traffic_mirror_session_vlan(self):
        (br_int_patch_ofport, source_local_vlan, source_mac_addr,
         source_ofport, target_local_vlan, target_mac_addr, tm_session,
         tm_session_vni, tun_patch_ofport,
         tunnel_ofport) = (TrafficMirrorMockObject().set_env(
             self.tm_ext,
             allowed_address_mac="aa:bb:cc:dd:ee:ff",
             port_security_enabled=True,
             port_binding_network='vlan'))
        int_patch_ofport = 110
        allowed_address_mac = "aa:bb:cc:dd:ee:ff"
        mock.patch.object(self.tm_ext, 'install_source_port_flow').start()
        mock.patch.object(self.tm_ext,
                          'install_output_flow_from_br_int').start()
        mock.patch.object(self.tm_ext, 'add_br_mirror_output_flow').start()
        mock.patch.object(self.tm_ext,
                          'install_accept_flow_from_br_mirror').start()
        mock.patch.object(self.tm_ext, 'install_accept_flow_to_target').start()

        self.tm_ext.create_traffic_mirror_session(None, tm_session)
        source_port_calls = [
            mock.call(direction='ingress',
                      dl_src=allowed_address_mac,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      port_security=True,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=allowed_address_mac,
                      dl_vlan=source_local_vlan,
                      port_security=True,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=allowed_address_mac,
                      dl_vlan=source_local_vlan,
                      in_port=int_patch_ofport,
                      is_phy_ofport=True,
                      port_security=True,
                      priority=60),
            mock.call(direction='ingress',
                      dl_src=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      port_security=True,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      port_security=True,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=int_patch_ofport,
                      is_phy_ofport=True,
                      port_security=True,
                      priority=60)
        ]
        self.assertEqual(
            sorted(self.tm_ext.install_source_port_flow.call_args_list),
            sorted(source_port_calls))

        output_flow_calls = [
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=allowed_address_mac,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=allowed_address_mac,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_dst='aa:bb:cc:dd:ee:ff',
                      dl_vlan=source_local_vlan,
                      mod_dl_dst=target_mac_addr,
                      mod_dl_src=allowed_address_mac,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=source_mac_addr,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      mod_dl_dst=target_mac_addr,
                      mod_dl_src=source_mac_addr,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000)
        ]
        self.assertEqual(
            sorted(self.tm_ext.install_output_flow_from_br_int.call_args_list),
            sorted(output_flow_calls))

        accept_flow_calls = [mock.call(mod_vlan_id=2, tun_id=10000)]
        self.assertEqual(
            sorted(
                self.tm_ext.install_accept_flow_from_br_mirror.call_args_list),
            sorted(accept_flow_calls))
        accept_flow_calls = [
            mock.call(br_int_patch_ofport,
                      dl_vlan=target_local_vlan,
                      dst_mac=target_mac_addr,
                      target_port=123)
        ]
        self.assertEqual(
            accept_flow_calls,
            self.tm_ext.install_accept_flow_to_target.call_args_list)

    def test_delete_traffic_mirror_session_vlan(self):
        (br_int_patch_ofport, source_local_vlan, source_mac_addr,
         source_ofport, target_local_vlan, target_mac_addr, tm_session,
         tm_session_vni, tun_patch_ofport,
         tunnel_ofport) = (TrafficMirrorMockObject().set_env(
             self.tm_ext,
             allowed_address_mac="aa:bb:cc:dd:ee:ff",
             port_security_enabled=True,
             port_binding_network='vlan'))
        allowed_address_mac = "aa:bb:cc:dd:ee:ff"
        mock.patch.object(self.tm_ext, 'uninstall_source_port_flow').start()
        mock.patch.object(self.tm_ext, 'uninstall_filter_flows').start()
        mock.patch.object(self.tm_ext,
                          'delete_output_flow_from_br_int').start()
        mock.patch.object(self.tm_ext, '_delete_traffic_flow').start()
        mock.patch.object(self.tm_ext,
                          'uninstall_accept_flow_from_br_mirror').start()
        mock.patch.object(self.tm_ext,
                          'uninstall_accept_flow_to_target').start()
        mock.patch.object(self.tm_ext, 'delete_tunnel_port').start()

        self.tm_ext.delete_traffic_mirror_session(None, tm_session)
        source_port_calls = [
            mock.call(direction='ingress',
                      dl_src=allowed_address_mac,
                      in_port=source_ofport),
            mock.call(direction='egress', dl_dst=allowed_address_mac),
            mock.call(direction='ingress',
                      dl_src=source_mac_addr,
                      in_port=source_ofport),
            mock.call(direction='egress', dl_dst=source_mac_addr),
        ]
        for source_port_call in source_port_calls:
            self.assertIn(
                source_port_call,
                self.tm_ext.uninstall_source_port_flow.call_args_list)
        self.assertEqual(
            len(source_port_calls),
            len(self.tm_ext.uninstall_source_port_flow.call_args_list))

        filter_flow_calls = [
            mock.call(direction='ingress',
                      in_port=source_ofport,
                      mac_addr=allowed_address_mac),
            mock.call(direction='egress',
                      dl_vlan=source_local_vlan,
                      mac_addr=allowed_address_mac),
            mock.call(direction='ingress',
                      in_port=source_ofport,
                      mac_addr=source_mac_addr),
            mock.call(direction='egress',
                      dl_vlan=source_local_vlan,
                      mac_addr=source_mac_addr),
        ]
        for filter_flow_call in filter_flow_calls:
            self.assertIn(filter_flow_call,
                          self.tm_ext.uninstall_filter_flows.call_args_list)
        self.assertEqual(
            len(filter_flow_calls),
            len(self.tm_ext.uninstall_filter_flows.call_args_list))

        output_flow_calls = [
            mock.call(dl_src=source_mac_addr, in_port=source_ofport),
            mock.call(dl_dst=source_mac_addr),
            mock.call(dl_src=allowed_address_mac, in_port=source_ofport),
            mock.call(dl_dst=allowed_address_mac),
        ]
        for output_flow_call in output_flow_calls:
            self.assertIn(
                output_flow_call,
                self.tm_ext.delete_output_flow_from_br_int.call_args_list)
        self.assertEqual(
            len(self.tm_ext.delete_output_flow_from_br_int.call_args_list),
            len(output_flow_calls))

        traffic_flow_calls = [
            mock.call(dl_src=source_mac_addr, table=0),
            mock.call(dl_dst=source_mac_addr, table=0),
            mock.call(dl_src=allowed_address_mac, table=0),
            mock.call(dl_dst=allowed_address_mac, table=0),
        ]
        for traffic_flow_call in traffic_flow_calls:
            self.assertIn(traffic_flow_call,
                          self.tm_ext._delete_traffic_flow.call_args_list)
        self.assertEqual(len(self.tm_ext._delete_traffic_flow.call_args_list),
                         len(traffic_flow_calls))

        self.assertEqual(
            [mock.call(tun_id=10000)],
            self.tm_ext.uninstall_accept_flow_from_br_mirror.call_args_list)
        self.assertEqual(
            [mock.call(br_int_patch_ofport, dst_mac=target_mac_addr)],
            self.tm_ext.uninstall_accept_flow_to_target.call_args_list)
        self.tm_ext.delete_tunnel_port.assert_called_with('**********', 10000)

    def test_create_traffic_mirror_session_disable_port_security(self):
        (br_int_patch_ofport, source_local_vlan, source_mac_addr,
         source_ofport, target_local_vlan, target_mac_addr, tm_session,
         tm_session_vni, tun_patch_ofport,
         tunnel_ofport) = (TrafficMirrorMockObject().set_env(
             self.tm_ext,
             port_security_enabled=False,
             port_binding_network='vxlan'))
        mock.patch.object(self.tm_ext, 'install_source_port_flow').start()
        mock.patch.object(self.tm_ext,
                          'install_output_flow_from_br_int').start()
        mock.patch.object(self.tm_ext, 'add_br_mirror_output_flow').start()
        mock.patch.object(self.tm_ext,
                          'install_accept_flow_from_br_mirror').start()
        mock.patch.object(self.tm_ext, 'install_accept_flow_to_target').start()

        self.tm_ext.create_traffic_mirror_session(None, tm_session)
        source_port_calls = [
            mock.call(direction='ingress',
                      dl_src=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      port_security=False,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      port_security=False,
                      priority=50),
            mock.call(direction='egress',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=tun_patch_ofport,
                      is_phy_ofport=True,
                      port_security=False,
                      priority=60)
        ]
        self.assertEqual(
            sorted(self.tm_ext.install_source_port_flow.call_args_list),
            sorted(source_port_calls))

        output_flow_calls = [
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_src=source_mac_addr,
                      in_port=source_ofport,
                      mod_dl_dst=target_mac_addr,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000),
            mock.call(br_int_patch_ofport,
                      False,
                      'vxlan',
                      dl_dst=source_mac_addr,
                      dl_vlan=source_local_vlan,
                      mod_dl_dst=target_mac_addr,
                      mod_dl_src=source_mac_addr,
                      mod_vlan_id=source_local_vlan,
                      packet_length=120,
                      vxlan_dl_dst=target_mac_addr,
                      vxlan_dl_src=source_mac_addr,
                      vxlan_ethertype='IPv4',
                      vxlan_nw_dst='***********',
                      vxlan_nw_src='************',
                      vxlan_vni=30000)
        ]
        self.assertEqual(
            sorted(self.tm_ext.install_output_flow_from_br_int.call_args_list),
            sorted(output_flow_calls))

        self.assertEqual(
            sorted(
                self.tm_ext.install_accept_flow_from_br_mirror.call_args_list),
            sorted([
                mock.call(mod_vlan_id=2, tun_id=10000),
            ]))
        accept_flow_calls = [
            mock.call(br_int_patch_ofport,
                      dl_vlan=target_local_vlan,
                      dst_mac=target_mac_addr,
                      target_port=123)
        ]
        self.assertEqual(
            accept_flow_calls,
            self.tm_ext.install_accept_flow_to_target.call_args_list)

    def test_delete_traffic_mirror_session_disable_port_security(self):
        (br_int_patch_ofport, source_local_vlan, source_mac_addr,
         source_ofport, target_local_vlan, target_mac_addr, tm_session,
         tm_session_vni, tun_patch_ofport,
         tunnel_ofport) = (TrafficMirrorMockObject().set_env(
             self.tm_ext,
             port_security_enabled=False,
             port_binding_network='vxlan'))
        mock.patch.object(self.tm_ext, 'uninstall_source_port_flow').start()
        mock.patch.object(self.tm_ext, 'uninstall_filter_flows').start()
        mock.patch.object(self.tm_ext,
                          'delete_output_flow_from_br_int').start()
        mock.patch.object(self.tm_ext, '_delete_traffic_flow').start()
        mock.patch.object(self.tm_ext,
                          'uninstall_accept_flow_from_br_mirror').start()
        mock.patch.object(self.tm_ext,
                          'uninstall_accept_flow_to_target').start()
        mock.patch.object(self.tm_ext, 'delete_tunnel_port').start()

        self.tm_ext.delete_traffic_mirror_session(None, tm_session)
        source_port_calls = [
            mock.call(direction='ingress',
                      dl_src=source_mac_addr,
                      in_port=source_ofport),
            mock.call(direction='egress', dl_dst=source_mac_addr),
        ]
        for source_port_call in source_port_calls:
            self.assertIn(
                source_port_call,
                self.tm_ext.uninstall_source_port_flow.call_args_list)
        self.assertEqual(
            len(source_port_calls),
            len(self.tm_ext.uninstall_source_port_flow.call_args_list))

        filter_flow_calls = [
            mock.call(direction='ingress',
                      in_port=source_ofport,
                      mac_addr=source_mac_addr),
            mock.call(direction='egress',
                      dl_vlan=source_local_vlan,
                      mac_addr=source_mac_addr),
        ]
        for filter_flow_call in filter_flow_calls:
            self.assertIn(filter_flow_call,
                          self.tm_ext.uninstall_filter_flows.call_args_list)
        self.assertEqual(
            len(filter_flow_calls),
            len(self.tm_ext.uninstall_filter_flows.call_args_list))

        output_flow_calls = [
            mock.call(dl_src=source_mac_addr, in_port=source_ofport),
            mock.call(dl_dst=source_mac_addr),
        ]
        for output_flow_call in output_flow_calls:
            self.assertIn(
                output_flow_call,
                self.tm_ext.delete_output_flow_from_br_int.call_args_list)
        self.assertEqual(
            len(self.tm_ext.delete_output_flow_from_br_int.call_args_list),
            len(output_flow_calls))

        traffic_flow_calls = [
            mock.call(dl_src=source_mac_addr, table=0),
            mock.call(dl_dst=source_mac_addr, table=0),
        ]
        for traffic_flow_call in traffic_flow_calls:
            self.assertIn(traffic_flow_call,
                          self.tm_ext._delete_traffic_flow.call_args_list)
        self.assertEqual(len(self.tm_ext._delete_traffic_flow.call_args_list),
                         len(traffic_flow_calls))

        self.assertEqual(
            [mock.call(tun_id=10000)],
            self.tm_ext.uninstall_accept_flow_from_br_mirror.call_args_list)
        self.assertEqual([
            mock.call(br_int_patch_ofport, dst_mac=target_mac_addr),
        ], self.tm_ext.uninstall_accept_flow_to_target.call_args_list)
        self.tm_ext.delete_tunnel_port.assert_called_with('**********', 10000)

    def test_process_source_port_filter_actions(self):
        TrafficMirrorMockObject().set_env(self.tm_ext)
        mock.patch.object(self.tm_ext, 'install_filter_flows').start()
        filter_id = "0c8c94c1-7e0a-44c3-9d59-980a4ad45756"
        filter_rules = self.tm_ext.get_filter_rules(filter_id)
        self.tm_ext.process_source_port_filter_actions(
            self.tm_ext.source_port_dict, self.tm_ext.source_vif_port,
            self.tm_ext.source_port_vlan, filter_rules)
        filter_rule_calls = []
        _v4_addr, _v6_addr, mac_address = (
            self.tm_ext.get_all_address_from_port(
                self.tm_ext.source_port_dict))
        for rule in filter_rules.get('rules'):
            direction = rule.get('direction')
            ethertype = rule.get('ethertype')
            protocol = rule.get('protocol', 'all')
            src_cidr = rule.get('src_cidr')
            dst_cidr = rule.get('dst_cidr')
            src_port_range_min = rule.get('src_port_range_min')
            src_port_range_max = rule.get('src_port_range_max')
            dst_port_range_min = rule.get('dst_port_range_min')
            dst_port_range_max = rule.get('dst_port_range_max')
            action = rule.get('action', 'accept')
            priority = rule.get('priority', 0)
            for mac_addr in mac_address:
                filter_rule_calls.append(
                    mock.call(ethertype=ethertype,
                              protocol=protocol,
                              direction=direction,
                              in_port=self.tm_ext.source_vif_port.ofport,
                              dl_vlan=self.tm_ext.source_port_vlan,
                              mac_addr=mac_addr,
                              src_cidr=src_cidr,
                              dst_cidr=dst_cidr,
                              src_port_range_min=src_port_range_min,
                              src_port_range_max=src_port_range_max,
                              dst_port_range_min=dst_port_range_min,
                              dst_port_range_max=dst_port_range_max,
                              priority=priority,
                              action=action))
        self.assertEqual(filter_rule_calls,
                         self.tm_ext.install_filter_flows.call_args_list)
