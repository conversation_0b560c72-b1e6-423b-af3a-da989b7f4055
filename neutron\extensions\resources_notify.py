#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc
import os

from neutron_lib.api import attributes
from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import faults
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import directory
from neutron_lib.services import base as service_base
from oslo_log import log as logging
import six
import webob.exc

from neutron._i18n import _
from neutron.api import extensions
from neutron.api.v2 import resource
from neutron.extensions import _resources_notify as apidef
from neutron import wsgi

extensions.append_api_extensions_path([os.path.dirname(__file__)])

LOG = logging.getLogger(__name__)


class Resources_notify(api_extensions.APIExtensionDescriptor):
    """Resources Notify API extension."""
    api_definition = apidef

    @classmethod
    def get_resources(cls):
        """Returns Ext Resources."""
        controller = resource.Resource(ResourcesNotifyController(),
                                       faults.FAULT_MAP)
        ext = extensions.ResourceExtension(apidef.RESOURCE_NAME, controller)
        return [ext]

    @classmethod
    def get_plugin_interface(cls):
        return ResourcesNotifyPluginBase


@six.add_metaclass(abc.ABCMeta)
class ResourcesNotifyPluginBase(service_base.ServicePluginBase):

    @classmethod
    def get_plugin_type(cls):
        return apidef.RESOURCES_NOTIFY

    def get_plugin_description(self):
        return "Resources Notify Service Plugin"

    @abc.abstractmethod
    def resources_notify(self, context, body, **kwargs):
        pass


class ResourcesNotifyController(wsgi.Controller):
    def create(self, request, body, **kwargs):
        # POST /v2.0/resources_notify
        # body: "resources_notify": {
        #   "resource_type": "port",
        #   "event_type": "created",
        #   "resource" : {"port": {"..."}}
        # }
        if not request.context.is_admin:
            reason = _("Only admin is authorized "
                       "to access resources notify API")
            raise n_exc.AdminRequired(reason=reason)
        self._validate_body(body)
        plugin = directory.get_plugin(apidef.RESOURCES_NOTIFY)
        return plugin.resources_notify(request.context, body)

    def index(self, request, **kwargs):
        # not supported
        # GET /v2.0/resources_notify?{key}
        raise webob.exc.HTTPNotFound("not supported")

    def show(self, request, id, **kwargs):
        # not supported
        # GET /v2.0/resources_notify/{key}
        raise webob.exc.HTTPNotFound("not supported")

    def update(self, request, id, **kwargs):
        # not supported
        # PUT /v2.0/resources_notify/{key}
        raise webob.exc.HTTPNotFound("not supported")

    def delete(self, request, id, **kwargs):
        # not supported
        # DELETE /v2.0/resources_notify/{key}
        raise webob.exc.HTTPNotFound("not supported")

    def _validate_body(self, body):
        if not body:
            raise webob.exc.HTTPBadRequest(_("Resource body required"))

        LOG.info("Request body: %(body)s", {'body': body})
        resources_notify = apidef.RESOURCE_NAME
        if resources_notify in body and not body[resources_notify]:
            raise webob.exc.HTTPBadRequest(_("Resources required"))
        res_dict = body.get(resources_notify)

        if res_dict is None:
            msg = _("Unable to find '%s' in request body") % resources_notify
            raise webob.exc.HTTPBadRequest(msg)
        if not isinstance(res_dict, dict):
            msg = _("Object '%s' contains invalid data") % resources_notify
            raise webob.exc.HTTPBadRequest(msg)

        if 'resource' in res_dict and not res_dict['resource']:
            raise webob.exc.HTTPBadRequest(_("Resource_data required"))

        attr_info = apidef.RESOURCE_ATTRIBUTE_MAP[apidef.RESOURCE_NAME]
        attr_ops = attributes.AttributeInfo(attr_info)
        attr_ops.verify_attributes(res_dict)
        attr_ops.convert_values(res_dict, exc_cls=webob.exc.HTTPBadRequest)
