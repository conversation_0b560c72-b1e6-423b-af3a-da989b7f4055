#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr

from neutron_lib.api.definitions import portbindings
from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import constants as lib_constants
from neutron_lib.db import utils as db_utils
from neutron_lib.objects import exceptions as obj_exc
from neutron_lib.plugins import directory
from neutron_lib.plugins.ml2 import api
from oslo_config import cfg
from oslo_db import exception as db_exc
from oslo_log import log as logging
from oslo_utils import uuidutils

from neutron._i18n import _
from neutron.api.rpc.callbacks import events as rpc_events
from neutron.api.rpc.handlers import resources_rpc
from neutron.common import utils
from neutron.conf.services import traffic_mirror as traffic_mirror_conf
from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.db import db_base_plugin_common
from neutron.db.models import traffic_mirror as tm_model
from neutron.extensions import _traffic_mirror as api_def
from neutron.extensions import traffic_mirror as ext_traffic_mirror
from neutron.objects import base as base_obj
from neutron.objects import traffic_mirror as traffic_mirror_obj
from neutron.services.traffic_mirror.common import exceptions as tm_exc

LOG = logging.getLogger(__name__)
traffic_mirror_conf.register_traffic_mirror_opts()


DEVICE_OWNER_MIRROR_TUNNEL = (
        lib_constants.DEVICE_OWNER_NETWORK_PREFIX + 'mirror_tunnel')


@resource_extend.has_resource_extenders
@registry.has_registry_receivers
class TrafficMirrorPlugin(ext_traffic_mirror.TrafficMirrosPluginBase):
    supported_extension_aliases = [api_def.ALIAS]

    __native_pagination_support = True
    __native_sorting_support = True

    def __init__(self):
        super(TrafficMirrorPlugin, self).__init__()
        self.core_plugin = directory.get_plugin()
        self.push_api = resources_rpc.ResourcesPushRpcApi()

        self.conf = cfg.CONF.traffic_mirror
        self.max_ingress_rules = (
            self.conf.max_ingress_rule_per_traffic_mirror_filter)
        self.max_egress_rules = (
            self.conf.max_egress_rule_per_traffic_mirror_filter)
        self.max_sources = self.conf.max_source_per_traffic_mirror_session
        self.max_sessions = self.conf.max_traffic_mirror_session_per_source

    def create_traffic_mirror_session(self, context, traffic_mirror_session):
        tm_session = traffic_mirror_session['traffic_mirror_session']
        tm_session.pop('tenant_id', None)
        source_ids = tm_session.pop('traffic_mirror_sources', [])
        target = tm_session.get('traffic_mirror_target_port_id', None)
        self._check_traffic_mirror_session_sources_limit(context, source_ids)
        self._check_traffic_mirror_session_sources_target(context, source_ids,
                                                          target)

        try:
            with db_api.context_manager.writer.using(context):
                tm_session_obj = traffic_mirror_obj.TrafficMirrorSession(
                    context, **tm_session)
                tm_session_obj.create()

                segment = self.core_plugin.type_manager._allocate_segment(
                    context, network_type='vxlan')
                if not segment:
                    raise tm_exc.TrafficMirrorSessionCreateError(
                        error=_('No free VXLAN segments available'))

                traffic_mirror_obj.TrafficMirrorSessionSegment(
                    context,
                    traffic_mirror_session_id=tm_session_obj['id'],
                    segmentation_id=segment[api.SEGMENTATION_ID]).create()

                self._create_or_update_traffic_mirror_source_bindings(
                    context, tm_session_obj['id'], source_ids)

                tm_session_obj.update()
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise tm_exc.TrafficMirrorSessionCreateError(error=e)

        self.push_api.push(context, [tm_session_obj], rpc_events.CREATED)
        return self._make_traffic_mirror_session_dict(tm_session_obj)

    def update_traffic_mirror_session(
            self, context, id, traffic_mirror_session):
        tm_session = traffic_mirror_session['traffic_mirror_session']
        source_ids = tm_session.pop('traffic_mirror_sources', [])
        target = tm_session.get('traffic_mirror_target_port_id', None)
        self._check_traffic_mirror_session_sources_limit(context, source_ids,
                                                         session_id=id)
        self._check_traffic_mirror_session_sources_target(
            context, source_ids, target)
        tm_session_obj = self._get_traffic_mirror_session(context, id)

        try:
            with db_api.context_manager.writer.using(context):
                tm_session_obj.update_fields(tm_session)

                if source_ids:
                    self._create_or_update_traffic_mirror_source_bindings(
                        context, id, source_ids)
                tm_session_obj.update()
        except db_exc.DBReferenceError:
            raise tm_exc.TrafficMirrorSessionUpdateError(id=id)
        self.push_api.push(context, [tm_session_obj], rpc_events.UPDATED)
        return self._make_traffic_mirror_session_dict(tm_session_obj)

    def delete_traffic_mirror_session(self, context, id):
        tm_session_obj = self._get_traffic_mirror_session(context, id)

        with db_api.context_manager.writer.using(context):
            segment = {api.NETWORK_TYPE: 'vxlan',
                       api.SEGMENTATION_ID: tm_session_obj['segmentation_id']}
            self.core_plugin.type_manager.release_network_segment(
                context, segment)

            tm_session_obj.delete()

        self.push_api.push(context, [tm_session_obj], rpc_events.DELETED)

    def get_traffic_mirror_session(self, context, id, fields=None):
        tm_session_obj = self._get_traffic_mirror_session(context, id)
        return self._make_traffic_mirror_session_dict(tm_session_obj)

    def get_traffic_mirror_sessions(self, context, filters=None, fields=None,
                                    sorts=None, limit=None, marker=None,
                                    page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        tm_session_objs = traffic_mirror_obj.TrafficMirrorSession.get_objects(
            context, _pager=pager, **filters)

        return [self._make_traffic_mirror_session_dict(obj)
                for obj in tm_session_objs]

    def _check_traffic_mirror_session_sources_limit(self, context, sources,
                                                    session_id=None):
        if len(sources) > self.max_sources:
            details = (_("number of sources exceeds the limit, now "
                         "has %(num)s, limit is %(limit)s.") %
                       {'num': len(sources), 'limit': self.max_sources})
            raise tm_exc.TrafficMirrorSessionLimitExceeded(details=details)

        session_sources = []
        if session_id:
            with db_api.context_manager.reader.using(context):
                Binding = tm_model.TrafficMirrorSourceBinding
                query = context.session.query(Binding.source_port_id)
                query = query.filter(
                    Binding.traffic_mirror_session_id == session_id)
                session_sources = [s[0] for s in query.all()]

        for source_id in sources:
            session_cnt = traffic_mirror_obj.TrafficMirrorSourceBinding.count(
                context, source_port_id=source_id)
            if (session_cnt >= self.max_sessions and
                    source_id not in session_sources):
                details = (_("source %(id)s has been bound to %(num)s "
                             "sessions and the limit is %(limit)s.") %
                           {'id': source_id, 'num': session_cnt,
                            'limit': self.max_sessions})
                raise tm_exc.TrafficMirrorSessionLimitExceeded(details=details)

    def _check_traffic_mirror_session_sources_target(
            self, context, sources, target):
        if target in sources:
            raise tm_exc.TrafficMirrorSessionSourcesTargetConflict(
                details=_("source and target cannot be same."))

        for source_id in sources:
            target_as_source = (
                traffic_mirror_obj.TrafficMirrorSession.get_objects(
                    context, traffic_mirror_target_port_id=source_id))
            if target_as_source:
                details = _("source %s is already used as target.") % source_id
                raise tm_exc.TrafficMirrorSessionSourcesTargetConflict(
                    details=details)

        source_as_target = (
            traffic_mirror_obj.TrafficMirrorSourceBinding.get_objects(
                context, source_port_id=target))
        if source_as_target:
            details = _("target %s is already used as source.") % target
            raise tm_exc.TrafficMirrorSessionSourcesTargetConflict(
                details=details)

    def _create_or_update_traffic_mirror_source_bindings(
            self, context, tm_session_id, source_ids):
        traffic_mirror_obj.TrafficMirrorSourceBinding.delete_objects(
            context, traffic_mirror_session_id=tm_session_id)

        for source_id in source_ids:
            traffic_mirror_obj.TrafficMirrorSourceBinding(
                context,
                traffic_mirror_session_id=tm_session_id,
                source_port_id=source_id).create()

    def _get_traffic_mirror_session(self, context, id):
        tm_session_obj = traffic_mirror_obj.TrafficMirrorSession.get_object(
            context, id=id)
        if not tm_session_obj:
            raise tm_exc.TrafficMirrorSessionNotFound(id=id)
        return tm_session_obj

    def _make_traffic_mirror_session_dict(
            self, traffic_mirror_session, fields=None):
        res = traffic_mirror_session.to_dict()
        sources = [s['source_port_id']
                   for s in traffic_mirror_session['traffic_mirror_sources']]
        res.update({'traffic_mirror_sources': sources})

        resource_extend.apply_funcs(api_def.TRAFFIC_MIRROR_SESSIONS, res,
                                    traffic_mirror_session.db_obj)
        return db_utils.resource_fields(res, fields)

    @db_base_plugin_common.convert_result_to_dict
    def create_traffic_mirror_filter(self, context, traffic_mirror_filter):
        tm_filter = traffic_mirror_filter['traffic_mirror_filter']
        tm_filter.pop('tenant_id', None)

        try:
            with db_api.context_manager.writer.using(context):
                tm_filter_obj = traffic_mirror_obj.TrafficMirrorFilter(
                    context, **tm_filter)
                tm_filter_obj.create()
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise tm_exc.TrafficMirrorFilterCreateError(error=e)
        return tm_filter_obj

    def update_traffic_mirror_filter(self, context, id, traffic_mirror_filter):
        tm_filter = traffic_mirror_filter['traffic_mirror_filter']

        tm_filter_obj = self._get_traffic_mirror_filter(context, id)

        try:
            with db_api.context_manager.writer.using(context):
                tm_filter_obj.update_fields(tm_filter)
                tm_filter_obj.update()
        except db_exc.DBReferenceError:
            raise tm_exc.TrafficMirrorFilterUpdateError(id=id)
        self.push_api.push(context, [tm_filter_obj], rpc_events.UPDATED)
        return self._make_traffic_mirror_filter_dict(tm_filter_obj)

    def delete_traffic_mirror_filter(self, context, id):
        filters = {'traffic_mirror_filter_id': [id]}
        with db_api.context_manager.reader.using(context):
            sessions = self.get_traffic_mirror_sessions(context, filters)
            if sessions:
                raise tm_exc.TrafficMirrorFilterInUse(id=id)

        with db_api.context_manager.writer.using(context):
            tm_filter_obj = self._get_traffic_mirror_filter(context, id)
            tm_filter_obj.delete()
            self.push_api.push(context, [tm_filter_obj], rpc_events.DELETED)

    def get_traffic_mirror_filter(self, context, id, fields=None):
        tm_filter_obj = self._get_traffic_mirror_filter(context, id)
        return self._make_traffic_mirror_filter_dict(tm_filter_obj)

    def get_traffic_mirror_filters(self, context, filters=None, fields=None,
                                   sorts=None, limit=None, marker=None,
                                   page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        tm_filter_objs = traffic_mirror_obj.TrafficMirrorFilter.get_objects(
            context, _pager=pager, **filters)

        return [self._make_traffic_mirror_filter_dict(obj, fields)
                for obj in tm_filter_objs]

    def _get_traffic_mirror_filter(self, context, id):
        obj = traffic_mirror_obj.TrafficMirrorFilter.get_object(context, id=id)
        if obj is None:
            raise tm_exc.TrafficMirrorFilterNotFound(id=id)
        return obj

    def _make_traffic_mirror_filter_dict(self, traffic_mirror_filter,
                                         fields=None):
        res = traffic_mirror_filter.to_dict()
        res['ingress_rules'] = []
        res['egress_rules'] = []

        if traffic_mirror_filter.ingress_rules:
            res['ingress_rules'] = [
                self._make_traffic_mirror_filter_rule_dict(r)
                for r in traffic_mirror_filter.ingress_rules]

        if traffic_mirror_filter.egress_rules:
            res['egress_rules'] = [
                self._make_traffic_mirror_filter_rule_dict(r)
                for r in traffic_mirror_filter.egress_rules]

        resource_extend.apply_funcs(api_def.TRAFFIC_MIRROR_FILTERS, res,
                                    traffic_mirror_filter.db_obj)
        return db_utils.resource_fields(res, fields)

    def _make_traffic_mirror_filter_rule_dict(self, rule, fields=None):
        res = {'id': rule['id'],
               'project_id': rule['project_id'],
               'description': rule['description'],
               'traffic_mirror_filter_id': rule['traffic_mirror_filter_id'],
               'direction': rule['direction'],
               'ethertype': rule['ethertype'],
               'protocol': rule['protocol'] or 'all',
               'src_cidr': str(rule['src_cidr']),
               'dst_cidr': str(rule['dst_cidr']),
               'action': rule['action'],
               'priority': rule['priority']}

        if rule['src_port_range_min'] and rule['src_port_range_max']:
            res['src_port_range'] = (
                '%s:%s' %
                (rule['src_port_range_min'], rule['src_port_range_max']))
        else:
            res['src_port_range'] = None

        if rule['dst_port_range_min'] and rule['dst_port_range_max']:
            res['dst_port_range'] = (
                '%s:%s' %
                (rule['dst_port_range_min'], rule['dst_port_range_max']))
        else:
            res['dst_port_range'] = None

        resource_extend.apply_funcs(api_def.TRAFFIC_MIRROR_FILTER_RULES,
                                    res, rule.db_obj)
        return db_utils.resource_fields(res, fields)

    def create_traffic_mirror_filter_rule(
            self, context, traffic_mirror_filter_rule):
        tmf_rule_obj = self._create_traffic_mirror_filter_rule(
            context, traffic_mirror_filter_rule)
        tm_filter_objs = traffic_mirror_obj.TrafficMirrorFilter.get_objects(
            context, id=tmf_rule_obj.traffic_mirror_filter_id)
        self.push_api.push(context, tm_filter_objs, rpc_events.UPDATED)
        return self._make_traffic_mirror_filter_rule_dict(tmf_rule_obj)

    def _create_traffic_mirror_filter_rule(
            self, context, traffic_mirror_filter_rule):
        tmf_rule = traffic_mirror_filter_rule['traffic_mirror_filter_rule']
        tmf_rule.pop('tenant_id', None)

        self._validate_traffic_mirror_filter_rule(context, tmf_rule)
        self._check_traffic_mirror_filter_rule_limit(context, tmf_rule)
        self._set_traffic_mirror_filter_rule(tmf_rule)

        self._check_duplicate_rules(
            context, tmf_rule.get('traffic_mirror_filter_id'), [tmf_rule])
        try:
            with db_api.context_manager.writer.using(context):
                tmf_rule_obj = traffic_mirror_obj.TrafficMirrorFilterRule(
                    context, **tmf_rule)
                tmf_rule_obj.create()
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise tm_exc.TrafficMirrorFilterRuleCreateError(error=e)
        return tmf_rule_obj

    def _check_traffic_mirror_filter_rule_limit(self, context, rule):
        direction = rule.get('direction')

        filters = {
            'traffic_mirror_filter_id': rule['traffic_mirror_filter_id']}
        if direction == 'ingress':
            filters['direction'] = 'ingress'
            ingress_cnt = traffic_mirror_obj.TrafficMirrorFilterRule.count(
                context, **filters)
            if ingress_cnt >= self.max_ingress_rules:
                raise tm_exc.TrafficMirrorFilterRuleLimitExceeded(
                    direction=direction, num=ingress_cnt,
                    limit=self.max_ingress_rules)
        else:
            filters['direction'] = 'egress'
            egress_cnt = traffic_mirror_obj.TrafficMirrorFilterRule.count(
                context, **filters)
            if egress_cnt >= self.max_egress_rules:
                raise tm_exc.TrafficMirrorFilterRuleLimitExceeded(
                    direction=direction, num=egress_cnt,
                    limit=self.max_egress_rules)

    def _set_traffic_mirror_filter_rule(self, rule):
        src_port_range = rule.pop('src_port_range', None)
        dst_port_range = rule.pop('dst_port_range', None)
        if src_port_range:
            src_range_min, src_range_max = src_port_range.split(':')
            rule['src_port_range_min'] = int(src_range_min)
            rule['src_port_range_max'] = int(src_range_max)
        if dst_port_range:
            dst_range_min, dst_range_max = dst_port_range.split(':')
            rule['dst_port_range_min'] = int(dst_range_min)
            rule['dst_port_range_max'] = int(dst_range_max)

        src_cidr = rule.get('src_cidr')
        dst_cidr = rule.get('dst_cidr')
        if src_cidr:
            rule['src_cidr'] = utils.AuthenticIPNetwork(src_cidr)
        if dst_cidr:
            rule['dst_cidr'] = utils.AuthenticIPNetwork(dst_cidr)

        if rule.get('protocol') == 'all':
            rule['protocol'] = None

        return rule

    def _validate_port_range(self, rule):
        src_port_range = rule.get('src_port_range')
        dst_port_range = rule.get('dst_port_range')
        if src_port_range:
            min_port, max_port = src_port_range.split(':')
            min_port, max_port = int(min_port), int(max_port)
            if min_port == 0 or max_port == 0:
                raise tm_exc.TrafficMirrorFilterRuleInvalidPortValue(port=0)
            if min_port > max_port:
                raise tm_exc.TrafficMirrorFilterRuleInvalidPortRange()

        if dst_port_range:
            min_port, max_port = dst_port_range.split(':')
            min_port, max_port = int(min_port), int(max_port)
            if min_port == 0 or max_port == 0:
                raise tm_exc.TrafficMirrorFilterRuleInvalidPortValue(port=0)
            if min_port > max_port:
                raise tm_exc.TrafficMirrorFilterRuleInvalidPortRange()

    def _validate_cidr(self, rule):
        src_cidr = rule.get('src_cidr')
        if src_cidr:
            addr = netaddr.IPNetwork(src_cidr)
            rule['src_cidr'] = str(addr)
            if rule.get('ethertype') != 'IPv%d' % addr.version:
                error = (_("Invalid ethertype %(ethertype)s for "
                           "CIDR %(cidr)s") %
                         {'ethertype': rule.get('ethertype'),
                          'cidr': src_cidr})
                raise tm_exc.TrafficMirrorFilterRuleInvalid(error=error)

        dst_cidr = rule.get('dst_cidr')
        if dst_cidr:
            addr = netaddr.IPNetwork(dst_cidr)
            rule['dst_cidr'] = str(addr)
            if rule.get('ethertype') != 'IPv%d' % addr.version:
                error = (_('Invalid ethertype %(ethertype)s for '
                           'CIDR %(cidr)s') %
                         {'ethertype': rule.get('ethertype'),
                          'cidr': dst_cidr})
                raise tm_exc.TrafficMirrorFilterRuleInvalid(error=error)

    def _validate_ethertype_and_protocol(self, rule):
        if rule.get('protocol') in [
                lib_constants.PROTO_NAME_IPV6_ICMP_LEGACY, ]:
            if rule.get('ethertype') == lib_constants.IPv4:
                raise tm_exc.TrafficMirrorFilterRuleInvalid(
                    error=_('ICMPv6 is not supported for IPv4'))

    def _validate_traffic_mirror_filter_rule(self, context, rule):
        self._validate_port_range(rule)
        self._validate_cidr(rule)
        self._validate_ethertype_and_protocol(rule)

    def _rule_to_key(self, rule):
        def _normalize_rule_value(key, value):
            none_char = '-'
            if value is None:
                return none_char
            elif key in ['protocol', 'ethertype']:
                return value.lower()
            return str(value)

        comparison_keys = [
            'traffic_mirror_filter_id',
            'direction',
            'ethertype',
            'protocol',
            'src_cidr', 'dst_cidr',
            'src_port_range_min', 'src_port_range_max',
            'dst_port_range_min', 'dst_port_range_max',
            'priority',
            'action'
        ]
        return '_'.join([_normalize_rule_value(key, rule.get(key))
                         for key in comparison_keys])

    def _check_duplicate_rules(self, context,
                               traffic_mirror_filter_id, rules):
        rules_set = set()
        for rule in rules:
            rule_key = self._rule_to_key(rule)
            if rule_key in rules_set:
                raise tm_exc.TrafficMirrorFilterRuleDuplicateOrConflict()
            rules_set.add(rule_key)

        tm_filter = self._get_traffic_mirror_filter(
            context, traffic_mirror_filter_id)
        for rule in tm_filter.rules:
            rule_key = self._rule_to_key(rule)
            if rule_key in rules_set:
                raise tm_exc.TrafficMirrorFilterRuleDuplicateOrConflict()

    def update_traffic_mirror_filter_rule(
            self, context, id, traffic_mirror_filter_rule):
        tmf_rule = traffic_mirror_filter_rule['traffic_mirror_filter_rule']
        tmf_rule_obj = self._get_traffic_mirror_filter_rule(context, id)

        new_rule_dict = tmf_rule_obj.to_dict()
        new_rule_dict.update(tmf_rule)
        self._validate_traffic_mirror_filter_rule(context, new_rule_dict)
        self._set_traffic_mirror_filter_rule(tmf_rule)
        self._set_traffic_mirror_filter_rule(new_rule_dict)

        # Do not check duplicated, if only the description field is updated.
        if not (len(tmf_rule) == 1 and 'description' in tmf_rule):
            self._check_duplicate_rules(
                context, tmf_rule_obj.get('traffic_mirror_filter_id'),
                [new_rule_dict])
        try:
            with db_api.context_manager.writer.using(context):
                tmf_rule_obj.update_fields(tmf_rule)
                tmf_rule_obj.update()
        except db_exc.DBReferenceError:
            raise tm_exc.TrafficMirrorFilterRuleUpdateError(id=id)
        tm_filter_objs = traffic_mirror_obj.TrafficMirrorFilter.get_objects(
            context, id=tmf_rule_obj.traffic_mirror_filter_id)
        self.push_api.push(context, tm_filter_objs, rpc_events.UPDATED)
        return self._make_traffic_mirror_filter_rule_dict(tmf_rule_obj)

    def delete_traffic_mirror_filter_rule(self, context, id):
        tmf_rule_obj = self._get_traffic_mirror_filter_rule(context, id)
        with db_api.context_manager.writer.using(context):
            tmf_rule_obj.delete()
        tm_filter_objs = traffic_mirror_obj.TrafficMirrorFilter.get_objects(
            context, id=tmf_rule_obj.traffic_mirror_filter_id)
        self.push_api.push(context, tm_filter_objs, rpc_events.UPDATED)

    def _get_traffic_mirror_filter_rule(self, context, id):
        obj = traffic_mirror_obj.TrafficMirrorFilterRule.get_object(
            context, id=id)
        if obj is None:
            raise tm_exc.TrafficMirrorFilterRuleNotFound(id=id)
        return obj

    def get_traffic_mirror_filter_rule(self, context, id, fields=None):
        tmf_rule_obj = self._get_traffic_mirror_filter_rule(context, id)
        return self._make_traffic_mirror_filter_rule_dict(tmf_rule_obj, fields)

    def get_traffic_mirror_filter_rules(self, context, filters=None,
                                        fields=None, sorts=None, limit=None,
                                        marker=None, page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        tmf_rule_objs = traffic_mirror_obj.TrafficMirrorFilterRule.get_objects(
            context, _pager=pager, **filters)

        return [self._make_traffic_mirror_filter_rule_dict(obj, fields)
                for obj in tmf_rule_objs]

    def get_mirror_tunnel_network_info(self, context, host, **kwargs):
        result_dict = {'mirror_tunnel_network_id': '',
                       'mirror_tunnel_subnets': [],
                       'mirror_tunnel_port': None}

        agent_id = kwargs.get('agent_id')
        availability_zone = kwargs.get('availability_zone', 'nova')
        tenant_id = context.tenant_id or uuidutils.generate_uuid()
        mirror_net = traffic_mirror_obj.MirrorTunnelNetwork.get_object(
            context, availability_zone=availability_zone)
        if not mirror_net:
            net_args = {'network': {
                'tenant_id': tenant_id,
                'name': 'mirror_tunnel_network',
                'admin_state_up': True,
                'shared': False,
                'allocation_pools': lib_constants.ATTR_NOT_SPECIFIED,
            }}
            net = self.core_plugin.create_network(context, net_args)
            tag_plugin = directory.get_plugin('TAG')
            if tag_plugin:
                tag_plugin.update_tag(context, 'network', mirror_net['id'],
                                      'disableprivatefloating')
            LOG.debug("[Traffic Mirror] create mirror tunnel network: %s", net)
            try:
                with db_api.context_manager.writer.using(context):
                    mirror_net = traffic_mirror_obj.MirrorTunnelNetwork(
                        context, availability_zone=availability_zone,
                        network_id=net['id'])
                    mirror_net.create()
            except obj_exc.NeutronDbObjectDuplicateEntry:
                # mirror network has being created by others,
                # delete current network, and return.
                self.core_plugin.delete_network(context, net['id'])
                return result_dict

        subnets = self.core_plugin._get_subnets_by_network(
            context, mirror_net['network_id'])
        if not subnets:
            subnet_args = {'subnet': {
                'name': 'mirror_tunnel_subnet',
                'tenant_id': tenant_id,
                'enable_dhcp': False,
                'dns_nameservers': lib_constants.ATTR_NOT_SPECIFIED,
                'allocation_pools': lib_constants.ATTR_NOT_SPECIFIED,
                'host_routes': lib_constants.ATTR_NOT_SPECIFIED,
                'cidr': self.conf.mirror_tunnel_cidr,
                'ip_version': 4,
                'network_id': mirror_net['network_id']}}
            subnet = self.core_plugin.create_subnet(context, subnet_args)
            LOG.debug("[Traffic Mirror] create mirror tunnel subnet: %s",
                      subnet)
            result_dict['mirror_tunnel_subnets'] = [subnet]
        else:
            result_dict['mirror_tunnel_subnets'] = [s.to_dict()
                                                    for s in subnets]

        filters = {
            portbindings.HOST_ID: [host],
            'device_owner': [DEVICE_OWNER_MIRROR_TUNNEL],
            'network_id': [mirror_net['network_id']]
        }
        ports = self.core_plugin.get_ports(context, filters=filters)
        if len(ports) < 1:
            port_args = {'port': {
                            'network_id': mirror_net['network_id'],
                            'tenant_id': tenant_id,
                            'device_id': agent_id,
                            'name': 'mirror tunnel port',
                            'admin_state_up': True,
                            'fixed_ips': lib_constants.ATTR_NOT_SPECIFIED,
                            'device_owner': DEVICE_OWNER_MIRROR_TUNNEL,
                            portbindings.HOST_ID: host}}
            port = self.core_plugin.create_port(context, port_args)
            LOG.debug("[Traffic Mirror] create mirror tunnel port: %s", port)
            result_dict['mirror_tunnel_port'] = port
        else:
            result_dict['mirror_tunnel_port'] = ports[0]

        result_dict['mirror_tunnel_network_id'] = mirror_net['network_id']
        return result_dict

    @registry.receives(resources.NETWORK, [events.AFTER_DELETE])
    def delete_mirror_tunnel_network(self, rtype, event, trigger,
                                     context, network, **kwargs):
        mirror_nets = traffic_mirror_obj.MirrorTunnelNetwork.get_objects(
            context, network_id=network['id'])
        if mirror_nets:
            mirror_net = mirror_nets[0]
            with db_api.context_manager.writer.using(context):
                mirror_net.delete()
