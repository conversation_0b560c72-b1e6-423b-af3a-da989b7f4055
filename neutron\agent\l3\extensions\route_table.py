#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib.agent import l3_extension
from neutron_lib import constants
from neutron_lib.utils import helpers
from oslo_log import log as logging

from neutron.agent.linux import ip_lib
from neutron.common import coordination
from neutron.common import utils as common_utils

LOG = logging.getLogger(__name__)
DEFAULT_ROUTE_TABLE_RULE_PRIORITY = 500
ROUTE_TABLE_RULE_PRIORITY = 100


class RouteTableAgentExtension(l3_extension.L3AgentExtension):

    def __init__(self):
        super(RouteTableAgentExtension, self).__init__()
        self.router_rt_info = {}

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        pass

    def consume_api(self, agent_api):
        self.agent_api = agent_api

    def _get_router_info(self, router_id):
        router_info = self.agent_api.get_router_info(router_id)
        if router_info:
            return router_info
        LOG.debug("Router %s is not managed by this agent. "
                  "It was possibly deleted concurrently.", router_id)

    def _flush_routing_table(self, namespace, table_id):
        cmd = ['ip', 'route', 'flush', 'table', table_id]
        ip_wapper = ip_lib.IPWrapper(namespace=namespace)
        try:
            ip_wapper.netns.execute(cmd, log_fail_as_error=False)
        except RuntimeError as e:
            LOG.error(e.message)

    def _update_routing_table(self, operation, route, namespace, table_id):
        cmd = ['ip', 'route', operation, 'to', route['destination'],
               'via', route['nexthop'], 'table', table_id]
        ip_wrapper = ip_lib.IPWrapper(namespace=namespace)
        try:
            ip_wrapper.netns.execute(cmd, log_fail_as_error=False)
        except RuntimeError as e:
            # Change the following errors to warnings:
            # 1. Network is unreachable: the nexthop is not directly connected
            # 2. No such process: when deleting a route that does not exist
            if any(s in e.message for s in
                   ['Network is unreachable', 'No such process',
                    'Nexthop has invalid gateway']):
                LOG.warning(e.message)
            else:
                LOG.error(e.message)

    def _get_table_id(self, router_id, route_tables, id):
        table_id = route_tables.get(id)
        rt_info = self.router_rt_info.get(router_id)
        return table_id or rt_info['table_ids'].get(id)

    def _set_internal_ports_routes(self, ri, router_id, namespace,
                                   route_tables):
        internal_ports = ri.internal_ports
        for p in internal_ports:
            device_name = ri.get_internal_device_name(p['id'])
            device = ip_lib.IPDevice(device_name, namespace=namespace)
            for ip in p.get('fixed_ips', []):
                if not ip['visible']:
                    continue
                ip_cidr = common_utils.ip_to_cidr(ip['ip_address'],
                                                  ip['prefixlen'])
                cidr = netaddr.IPNetwork(ip_cidr).cidr
                for rt_id in route_tables.keys():
                    table_id = self._get_table_id(router_id, route_tables,
                                                  rt_id)
                    device.route.add_route(
                        cidr, proto='kernel', scope='link',
                        src=ip['ip_address'], table=table_id)

    def _set_external_gw_routes(self, ri, router_id, namespace, route_tables):
        ex_gw_port = ri.get_ex_gw_port()
        if not ex_gw_port:
            return
        ex_gw_port_id = ex_gw_port['id']

        interface_name = ri.get_external_device_name(ex_gw_port_id)
        gateway_ips = ri._get_external_gw_ips(ex_gw_port)
        device = ip_lib.IPDevice(interface_name, namespace=namespace)
        current_gateways = set()
        for ip_version in (constants.IP_VERSION_4, constants.IP_VERSION_6):
            gateway = device.route.get_gateway(ip_version=ip_version)
            if gateway and gateway.get('gateway'):
                current_gateways.add(gateway.get('gateway'))
        for ip in current_gateways - set(gateway_ips):
            for rt_id in route_tables.keys():
                table_id = self._get_table_id(router_id, route_tables, rt_id)
                device.route.delete_gateway(ip, table=table_id)
        for ip in gateway_ips:
            for rt_id in route_tables.keys():
                table_id = self._get_table_id(router_id, route_tables, rt_id)
                device.route.add_gateway(ip, table=table_id)

        ex_gw_fixed_ips = ex_gw_port.get('fixed_ips', [])
        for ip in ex_gw_fixed_ips:
            if not ip['visible']:
                continue
            ip_cidr = common_utils.ip_to_cidr(ip['ip_address'],
                                              ip['prefixlen'])
            cidr = netaddr.IPNetwork(ip_cidr).cidr
            for rt_id in route_tables.keys():
                table_id = self._get_table_id(router_id, route_tables, rt_id)
                device.route.add_route(cidr, proto='kernel', scope='link',
                                       src=ip['ip_address'], table=table_id)

    def binding_default_route_tables_rules(self, ri, default_rt,
                                           bindings, namespace,
                                           route_tables):
        internal_ports = ri.internal_ports
        default_rt_index = route_tables[default_rt]
        for port in internal_ports:
            subnets = port.get('subnets', [])
            for subnet in subnets:
                if subnet['cidr'] in bindings:
                    continue
                self._binding_route_table_rule(
                    namespace, str(subnet['cidr']),
                    default_rt_index, DEFAULT_ROUTE_TABLE_RULE_PRIORITY)

    def _binding_route_table_rule(self, namespace, ip, rt_index, priority):
        # Check if the rule already exists. If it does,
        # do not add it again. This is to avoid duplicate rules.
        ip_version = netaddr.IPNetwork(ip).version
        rules = ip_lib.list_ip_rules(namespace, ip_version)
        for rule in rules:
            if (rule['from'] == ip and
                    rule['table'] == str(rt_index)):
                LOG.warning('ip rule exist: %s', ip)
                return
        ip_lib.add_ip_rule(namespace, ip=ip, table=rt_index, priority=priority)

    def _unbinding_route_table_rule(self, namespace, ip, rt_index,
                                    priority):
        ip_lib.delete_ip_rule(namespace, ip=ip, table=rt_index,
                              priority=priority)

    def setup_default_routes(self, ri, router_id, namespace,
                             old_route_tables, route_tables):
        removes = set(old_route_tables.values()) - set(route_tables.values())
        for remove in removes:
            LOG.debug('flush router %(router_id)s route table %(table_id)',
                      {'router_id': router_id, 'table_id': remove})
            self._flush_routing_table(namespace, remove)
        self._set_internal_ports_routes(ri, router_id, namespace, route_tables)
        self._set_external_gw_routes(ri, router_id, namespace, route_tables)

    def setup_route_table_bindings(self, router_id, namespace, route_tables,
                                   default_rt, old_bindings, new_bindings):
        associates, disassociates = helpers.diff_list_of_dict(
            old_bindings, new_bindings)
        for associate in associates:
            LOG.debug('route table %(rt)s associate with subnet %(subnet)s',
                      {'rt': associate['routetable_id'],
                       'subnet': associate['subnet_id']})
            table_id = self._get_table_id(router_id,
                                          route_tables,
                                          associate['routetable_id'])
            self._unbinding_route_table_rule(
                namespace, associate['cidr'],
                route_tables[default_rt],
                DEFAULT_ROUTE_TABLE_RULE_PRIORITY)
            self._binding_route_table_rule(
                namespace, associate['cidr'],
                table_id, ROUTE_TABLE_RULE_PRIORITY)
        for disassociate in disassociates:
            LOG.debug('route table %(rt)s disassociate with subnet %(subnet)s',
                      {'rt': disassociate['routetable_id'],
                       'subnet': disassociate['subnet_id']})
            table_id = self._get_table_id(router_id,
                                          route_tables,
                                          disassociate['routetable_id'])
            self._unbinding_route_table_rule(
                namespace, disassociate['cidr'],
                table_id, ROUTE_TABLE_RULE_PRIORITY)
            self._binding_route_table_rule(
                namespace, disassociate['cidr'],
                route_tables[default_rt],
                DEFAULT_ROUTE_TABLE_RULE_PRIORITY)

    def setup_route_table_routes(self, router_id, namespace, route_tables,
                                 old_routes, new_routes):
        adds, removes = helpers.diff_list_of_dict(old_routes, new_routes)
        for route in adds:
            LOG.debug("Added route table route is '%s' ", route)
            for del_route in removes:
                if route['destination'] == del_route['destination']:
                    removes.remove(del_route)
            table_id = self._get_table_id(router_id, route_tables,
                                          route['routetable_id'])
            self._update_routing_table('replace', route, namespace, table_id)
        for route in removes:
            LOG.debug("Removed route table route is '%s'", route)
            table_id = self._get_table_id(router_id, route_tables,
                                          route['routetable_id'])
            self._update_routing_table('delete', route, namespace, table_id)

    @coordination.synchronized('route-table-router-{data[id]}')
    def process_route_table(self, context, data):
        ri = self._get_router_info(data['id'])
        namespace = self.get_router_namespace(ri)
        default_route_table = ri.router.get('default_route_table', None)
        route_tables = ri.router.get('route_tables', {})
        route_table_routes = ri.router.get('route_table_routes', [])
        subnet_bindings = ri.router.get('route_table_subnet_bindings', [])

        rt_info = self.router_rt_info.get(data['id'], {})
        if not rt_info:
            rt_info['routes'] = []
            rt_info['bindings'] = []
            rt_info['table_ids'] = {}

        if default_route_table is None:
            return
        LOG.info("Processing route table routes for router %s", data['id'])

        self.setup_default_routes(ri, data['id'], namespace,
                                  rt_info['table_ids'],
                                  route_tables)
        subnet_bindings_cidr = set([subnet_binding['cidr']
                                    for subnet_binding in rt_info['bindings']])
        self.binding_default_route_tables_rules(ri, default_route_table,
                                                subnet_bindings_cidr,
                                                namespace, route_tables)
        self.setup_route_table_routes(data['id'], namespace, route_tables,
                                      rt_info['routes'], route_table_routes)
        self.setup_route_table_bindings(data['id'], namespace,
                                        route_tables, default_route_table,
                                        rt_info['bindings'], subnet_bindings)
        rt_info['routes'] = route_table_routes
        rt_info['bindings'] = subnet_bindings
        rt_info['table_ids'] = route_tables
        self.router_rt_info.update({data['id']: rt_info})

    def get_router_namespace(self, router_info):
        # Legacy/HA router
        namespace = router_info.ns_name
        return namespace

    def add_router(self, context, data):
        self.process_route_table(context, data)

    def update_router(self, context, data):
        self.process_route_table(context, data)

    def delete_router(self, context, data):
        pass

    def ha_state_change(self, context, data):
        pass
