#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.


# The name of the extension.
NAME = 'Resources Notify'
RESOURCES_NOTIFY = "RESOURCES_NOTIFY"

# The alias of the extension.
ALIAS = 'resources-notify'

# The description of the extension.
DESCRIPTION = "Extension to handle notifications for resources created by <PERSON><PERSON>"

# The timestamp of when the extension was introduced.
UPDATED_TIMESTAMP = "2025-02-06T18:04:00-08:08"

# The name of the resource.
RESOURCE_NAME = "resources_notify"

# List of allowed resource types.
RESOURCES = ["network", "subnet", "port", "flow_log", "qos_policy", "qos_rule",
             "router", "floating_ip", "port_forwarding", "elastic_snat",
             "traffic_mirror", "security_group", "security_group_rule"]

# List of allowed event types.
EVENTS = ["created", "updated", "deleted"]

RESOURCE_ATTRIBUTE_MAP = {
    RESOURCE_NAME: {
        'resource_type':
            {'allow_post': True,
             'allow_put': False,
             'is_visible': True,
             'validate': {'type:values': RESOURCES},
             },
        'event_type':
            {'allow_post': True,
             'allow_put': False,
             'is_visible': True,
             'validate': {'type:values': EVENTS},
             },
        'resource':
            {'allow_post': True,
             'allow_put': False,
             'is_visible': True,
             'validate': {'type:dict': None},
             }
    }
}

# The list of required extensions.
REQUIRED_EXTENSIONS = []

# Whether or not this extension is simply signaling behavior to the user
# or it actively modifies the attribute map.
IS_SHIM_EXTENSION = False

# Whether the extension is marking the adoption of standardattr model for
# legacy resources, or introducing new standardattr attributes. False or
# None if the standardattr model is adopted since the introduction of
# resource extension.
# If this is True, the alias for the extension should be prefixed with
# 'standard-attr-'.
IS_STANDARD_ATTR_EXTENSION = False

# The subresource attribute map for the extension. It adds child resources
# to main extension's resource. The subresource map must have a parent and
# a parameters entry. If an extension does not need such a map, None can
# be specified (mandatory).
SUB_RESOURCE_ATTRIBUTE_MAP = {}

# The action map: it associates verbs with methods to be performed on
# the API resource.
ACTION_MAP = {}

# The list of optional extensions.
OPTIONAL_EXTENSIONS = []

# The action status.
ACTION_STATUS = {}
