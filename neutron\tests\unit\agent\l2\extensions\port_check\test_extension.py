# Copyright (c) 2023 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.


import collections

import mock
import netaddr

from neutron_lib import context
from oslo_config import cfg
from oslo_utils import importutils
from oslo_utils import uuidutils
from ryu.ofproto import ofproto_v1_3_parser

from neutron.agent.common import ovs_lib as ovs_lib
from neutron.agent.l2.extensions.port_check.flow_checker import privatefloating
from neutron.api.rpc.handlers import securitygroups_rpc as sg_rpc
from neutron.conf.plugins.ml2.drivers import ovs_conf
from neutron.objects import ports as port_obj
from neutron.services.port_check.drivers import firewall_driver
from neutron.services.port_check import plugin
from neutron.tests import base
from neutron.tests.unit.plugins.ml2.drivers.openvswitch.agent import fake_oflib

_uuid = uuidutils.generate_uuid

_EXTENSION_PACKAGE = 'neutron.agent.l2.extensions.port_check.extension'
_EXT_CLASS = _EXTENSION_PACKAGE + '.PortCheckAgentExtension'
_OVS_BRIDGE_MOD = 'neutron.services.port_check.drivers.openvswitch.ovs_bridge'


class FakeOFPFlowStats(object):
    table_id = 1
    priority = 30
    match = ofproto_v1_3_parser.OFPMatch()
    instructions = []


class FakeDatapath(object):
    id = mock.Mock()


class FakeMsg(object):
    body = [FakeOFPFlowStats()]
    datapath = FakeDatapath()


class FakeVifPort(object):
    def __init__(self, name):
        self.port_name = name


class PortCheckExtensionTestBase(base.BaseTestCase):
    def setUp(self):
        self.br_int = mock.Mock()
        self.phy_br = mock.Mock()
        self.meta_br = mock.Mock()

        self.fake_oflib_of = fake_oflib.patch_fake_oflib_of()
        self.fake_oflib_of.start()
        self.addCleanup(self.fake_oflib_of.stop)
        super(PortCheckExtensionTestBase, self).setUp()


class PortCheckAgentExtensionTestCase(PortCheckExtensionTestBase):
    def setUp(self):
        super(PortCheckAgentExtensionTestCase, self).setUp()
        ovs_conf.register_ovs_agent_opts(cfg=cfg.CONF)

        self.context = context.get_admin_context()

        self.ext = importutils.import_object(_EXT_CLASS)
        self.agent_api = mock.Mock()
        self.ext.init_loop = mock.Mock()
        self.ext.consume_api(self.agent_api)

        with mock.patch('ryu.base.app_manager.AppManager.get_instance') \
                as get_instance:
            get_instance.instantiate.return_value = mock.Mock()
            self.ext.initialize(None, None)

        self.ext.br_int = self.br_int
        self.ext.phy_br = self.phy_br
        self.ext.meta_br = self.meta_br
        self.ext.init_check_manager()

    @mock.patch(_EXT_CLASS + '._do_ovs_check')
    @mock.patch(_EXT_CLASS + '._do_firewall_check')
    @mock.patch(_EXT_CLASS + '._do_private_floating_network_check')
    def test_ports_check(self, ovs_check, fw_check, pf_check):
        port = port_obj.Port(id=_uuid(),
                             device_owner='device_owner')
        kwargs = {'ports': [port]}
        with mock.patch.object(port_obj.Port, 'obj_from_primitive',
                               return_value=port):
            with mock.patch.object(self.ext.ext_check_mgr,
                                   'do_extensions_flow_check') as ext_check:
                self.ext.enable_private_floating = True
                self.ext.ports_check(self.context, **kwargs)
                ovs_check.assert_called()
                fw_check.assert_called()
                pf_check.assert_called()
                ext_check.assert_called()

    def test__do_ovs_check_invalid_tag(self):
        result_map = collections.defaultdict(plugin.PortCheckResult)
        port = port_obj.Port(id=_uuid(), device_owner='device_owner')

        with mock.patch.object(self.ext.br_int,
                               'get_port_tag_by_name',
                               return_value=4095):
            self.ext._do_ovs_check(self.context, result_map, [port])
            self.assertEqual(['port %s has invalid tag 4095' % port.id],
                             result_map[port.id]['openvswitch'])

    def test__do_ovs_check_wrong_mac_and_id(self):
        port = port_obj.Port(id=_uuid(),
                             device_owner='device_owner',
                             mac_address=netaddr.EUI('64:16:7F:68:75:A1'))

        with mock.patch.object(self.ext.br_int, 'get_port_tag_by_name',
                               return_value=1):
            self.ext.br_int.get_vif_port_by_id = mock.Mock(
                side_effect=lambda x: FakeVifPort('tap' + port.id[:11]))

            result_map = collections.defaultdict(plugin.PortCheckResult)
            external_ids = {'iface-id': port.id, 'attached-mac': 'mac'}
            with mock.patch.object(self.ext.br_int, 'get_port_external_ids',
                                   return_value=external_ids):
                self.ext._do_ovs_check(self.context, result_map, [port])
                msg = 'Ovsdb port mac mac is not equal to port db mac %s'
                self.assertEqual([msg % '64-16-7F-68-75-A1'],
                                 result_map[port.id]['openvswitch'])

            result_map = collections.defaultdict(plugin.PortCheckResult)
            external_ids = {'iface-id': 'id',
                            'attached-mac': '64:16:7F:68:75:A1'}
            with mock.patch.object(self.ext.br_int, 'get_port_external_ids',
                                   return_value=external_ids):
                self.ext._do_ovs_check(self.context, result_map, [port])
                msg = 'port %s device %s has wrong port_id id.'
                self.assertEqual([msg % (port.id, 'tap' + port.id[:11])],
                                 result_map[port.id]['openvswitch'])

    @mock.patch.object(firewall_driver.OVSFirewallDriver, '_initialize_sg')
    @mock.patch('neutron.agent.linux.openvswitch_firewall.iptables.Helper')
    def _test_filrewall_check(self, mock_init_sg, mock_iptables,
                              stateless=False):
        result_map = collections.defaultdict(plugin.PortCheckResult)
        port = port_obj.Port(id=_uuid(),
                             device_owner='device_owner',
                             mac_address=netaddr.EUI('64:16:7F:68:75:A1'))
        devices_info = {
            'devices': {port.id: mock.Mock()},
            'security_groups': {'sg_id': mock.Mock()},
            'sg_member_ips': {'remote_sg_id': {'ether': ['addr']}},
        }

        driver = firewall_driver.OVSFirewallDriver
        if stateless:
            driver = firewall_driver.OVSStatelessFirewallDriver

        with mock.patch.object(sg_rpc, 'SecurityGroupServerRpcApi') \
                as mock_sg_rpc:
            _rpc = mock_sg_rpc.return_value
            _rpc.security_group_info_for_devices.return_value = devices_info
            with mock.patch.object(
                    driver, 'update_security_group_rules') as update_rules:
                with mock.patch.object(
                        driver,
                        'update_security_group_members') as update_members:
                    with mock.patch.object(
                            driver, 'prepare_port_filter') as prepare_filter:
                        self.ext.br_int.reports = []
                        self.ext._do_firewall_check(self.context,
                                                    result_map, [port])
                        update_rules.assert_called_with('sg_id', mock.ANY)
                        update_members.assert_called_with('remote_sg_id',
                                                          mock.ANY)
                        prepare_filter.assert_called_with(mock.ANY)

    def test__do_openvswitch_firewall_check(self):
        cfg.CONF.set_override('firewall_driver', 'openvswitch',
                              'SECURITYGROUP')
        self._test_filrewall_check()

    def test__do_openvswitch_stateless_firewall_check(self):
        cfg.CONF.set_override('firewall_driver', 'openvswitch_stateless',
                              'SECURITYGROUP')
        self._test_filrewall_check(stateless=True)

    @mock.patch.object(privatefloating, 'check_private_floating_flows')
    def test__do_private_floating_network_check(self,
                                                check_flows):
        result_map = collections.defaultdict(plugin.PortCheckResult)
        port = port_obj.Port(id=_uuid(),
                             device_owner='compute:fake')

        with mock.patch.object(ovs_lib.OVSBridge, 'get_port_ofport',
                               side_effect=[1, 2]), \
                mock.patch.object(ovs_lib.OVSBridge, 'get_port_tag_by_name',
                                  side_effect=[4, 5]), \
                mock.patch.object(ovs_lib.OVSBridge, 'get_vif_port_by_id',
                                  side_effect=lambda x: FakeVifPort(
                                      'fake-port-name')):
            except_p = {'port_tag': 4, 'port_ofport': 1}
            except_pfn = {'port_tag': 5, 'port_ofport': 2}
            privatefloating.HostInfos = mock.Mock()
            hostinfos = privatefloating.HostInfos.return_value
            hostinfos.privatefloating_port = {'id': _uuid()}
            self.ext._do_private_floating_network_check(self.context,
                                                        result_map,
                                                        [port])
            check_flows.assert_called_with(mock.ANY, port, mock.ANY,
                                           except_p, except_pfn, mock.ANY)


class RASpeakerFlowCheckTestCase(PortCheckExtensionTestBase):
    def setUp(self):
        super(RASpeakerFlowCheckTestCase, self).setUp()
        ovs_conf.register_ovs_agent_opts(cfg=cfg.CONF)
        cfg.CONF.set_override('extensions', ['ra_speaker'], group='agent')

        self.context = context.get_admin_context()

        self.ext = importutils.import_object(_EXT_CLASS)
        self.agent_api = mock.Mock()
        self.ext.init_loop = mock.Mock()
        self.ext.consume_api(self.agent_api)

        with mock.patch('ryu.base.app_manager.AppManager.get_instance') \
                as get_instance:
            get_instance.instantiate.return_value = mock.Mock()
            self.ext.initialize(None, None)
            self.ext.init_check_manager()

    def test_raspeaker_flow_check(self):
        result_map = collections.defaultdict(plugin.PortCheckResult)
        port = port_obj.Port(id=_uuid(),
                             device_owner='compute:fake',
                             mac_address=netaddr.EUI('64:16:7F:68:75:A1'))
        self.ext.br_int._get_dp = mock.Mock(
            return_value=[mock.Mock(), mock.Mock(), mock.Mock()])
        self.ext.br_int.get_port_ofport = mock.Mock(return_value=1)
        self.ext.br_int.get_vif_port_by_id = mock.Mock(
            side_effect=lambda x: FakeVifPort('fake-port-name'))
        with mock.patch.object(self.ext.of_map, 'exists') as exists:
            self.ext.ext_check_mgr.do_extensions_flow_check(
                context, result_map, [port])
            exists.assert_called()


class MetadataPathFlowCheckTestCase(PortCheckExtensionTestBase):
    def setUp(self):
        super(MetadataPathFlowCheckTestCase, self).setUp()
        ovs_conf.register_ovs_agent_opts(cfg=cfg.CONF)
        cfg.CONF.set_override('extensions', ['metadata_path'], group='agent')

        self.context = context.get_admin_context()

        self.ext = importutils.import_object(_EXT_CLASS)
        self.agent_api = mock.Mock()
        self.agent_api.bridge_mappings = {"meta": "br-meta"}
        self.agent_api.phys_ofports = {"meta": 2}
        self.ext.init_loop = mock.Mock()
        self.ext.consume_api(self.agent_api)

        with mock.patch('ryu.base.app_manager.AppManager.get_instance') \
                as get_instance:
            with mock.patch('neutron.agent.common.flows_process.'
                            'BridgeNetworkingDataPathFlows.set_path_br'):
                get_instance.instantiate.return_value = mock.Mock()
                self.ext.initialize(None, None)
                self.ext.init_check_manager()

    def test_metadata_flow_check(self):
        result_map = collections.defaultdict(plugin.PortCheckResult)
        port = port_obj.Port(id=_uuid(),
                             network_id=_uuid(),
                             device_owner='compute:fake',
                             mac_address=netaddr.EUI('64:16:7F:68:75:A1'),
                             fixed_ips=[port_obj.IPAllocation(
                                 subnet_id=_uuid(),
                                 ip_address='*******')])
        dhcp_port = port_obj.Port(id=_uuid(),
                                  mac_address=netaddr.EUI('64:16:7F:68:75:A2'),
                                  fixed_ips=[port_obj.IPAllocation(
                                      subnet_id=_uuid(),
                                      ip_address='*******')])

        metadata = self.ext.ext_check_mgr.extensions[0]
        metadata_gw_ip = '*******'
        metadata.metadata_gateway_ip = metadata_gw_ip
        metadata.meta_dev_vlan_id = 100
        metadata.ofport_snat_to_int = 1
        metadata.meta_br._get_dp = mock.Mock(
            return_value=[mock.Mock(), mock.Mock(), mock.Mock()])
        metadata.meta_br.get_port_ofport = mock.Mock(return_value=2)
        provider_ip = '*******'
        provider_mac = 'fa:16:ee:00:00:01'
        metadata.int_br.get_value_from_other_config = mock.Mock(
            side_effect=[provider_ip, provider_mac])
        metadata.int_br._get_dp = mock.Mock(
            return_value=[mock.Mock(), mock.Mock(), mock.Mock()])
        metadata.int_br.get_vif_port_by_id = mock.Mock(
            side_effect=lambda x: FakeVifPort('name'))
        metadata.rcache_api.get_resources = mock.Mock(return_value=[dhcp_port])

        with mock.patch.object(metadata.meta_br, 'network_path_defaults') \
                as network_path_defaults:
            with mock.patch.object(self.ext.of_map, 'exists') as exists:
                self.ext.ext_check_mgr.do_extensions_flow_check(
                    context, result_map, [port])
                network_path_defaults.assert_called_once_with(
                    pvid=100, to_int_ofport=1, metadata_ofport=2,
                    metadata_host_info={'gateway_ip': metadata_gw_ip,
                                        'provider_ip': metadata_gw_ip,
                                        'mac_address': 'fa:16:ee:00:00:01',
                                        'service_protocol_port': 80,
                                        'enable_metrics_proxy': False,
                                        'metrics_listen_port': 81})
                exists.assert_called()


class DHCPAgentFlowCheckTestCase(PortCheckExtensionTestBase):
    def setUp(self):
        super(DHCPAgentFlowCheckTestCase, self).setUp()
        ovs_conf.register_ovs_agent_opts(cfg=cfg.CONF)
        cfg.CONF.set_override('extensions', ['dhcp'], group='agent')
        cfg.CONF.set_override('tunnel_types', ['vxlan'], group='AGENT')
        cfg.CONF.set_override('enable_nonlocal_dhcp_req', True, group='DHCP')

        self.context = context.get_admin_context()

        self.ext = importutils.import_object(_EXT_CLASS)
        self.agent_api = mock.Mock()
        self.ext.init_loop = mock.Mock()
        self.ext.consume_api(self.agent_api)
        with mock.patch('ryu.base.app_manager.AppManager.get_instance') \
                as get_instance:
            get_instance.instantiate.return_value = mock.Mock()
            self.ext.initialize(None, None)
            self.ext.init_check_manager()

        self.dhcp_ext = self.ext.ext_check_mgr.extensions[0]
        self.dhcp_ext.int_br = mock.Mock()
        self.dhcp_ext.int_br.reports = []

        self.port = port_obj.Port(
            id=_uuid(),
            network_id=_uuid(),
            device_owner='compute:fake',
            mac_address=netaddr.EUI('64:16:7F:68:75:A1'),
            fixed_ips=[port_obj.IPAllocation(
                subnet_id=_uuid(),
                ip_address='*******')])

    def _test_dhcp_flow_check(self, except_calls, ipv4=True):
        self.agent_api.bridge_mappings = {"test": "br-test"}

        result_map = collections.defaultdict(plugin.PortCheckResult)

        with mock.patch.object(self.dhcp_ext.int_br, 'get_port_ofport',
                               return_value=1):
            self.ext.ext_check_mgr.do_extensions_flow_check(
                context, result_map, [self.port])
            self.dhcp_ext.int_br.allow_traditional_dhcp.assert_has_calls([
                mock.call(mock.ANY, True),
                mock.call(mock.ANY, True)])
            if ipv4:
                self.dhcp_ext.int_br.add_dhcp_ipv4_flow.assert_has_calls(
                    except_calls)
            else:
                self.dhcp_ext.int_br.add_dhcp_ipv6_flow.assert_has_calls(
                    except_calls)

    def test_ipv4_dhcp_flow_check(self):
        self.agent_api.bridge_mappings = {"test": "br-test"}
        except_calls = [mock.call(self.port.id, 1, self.port.mac_address)]
        self._test_dhcp_flow_check(except_calls)

        cfg.CONF.set_override('extra_allowed_device_owners', ['compute:fake'],
                              group='DHCP')
        except_calls = [mock.call(self.port.id, 1)]
        self._test_dhcp_flow_check(except_calls)

    def test_ipv6_dhcp_flow_check(self):
        self.agent_api.bridge_mappings = {"test": "br-test"}
        except_calls = [mock.call(self.port.id, 1, self.port.mac_address)]
        self._test_dhcp_flow_check(except_calls, ipv4=False)

        cfg.CONF.set_override('extra_allowed_device_owners', ['compute:fake'],
                              group='DHCP')
        except_calls = [mock.call(self.port.id, 1)]
        self._test_dhcp_flow_check(except_calls)


class ServiceDatapathFlowCheckTestCase(PortCheckExtensionTestBase):
    def setUp(self):
        super(ServiceDatapathFlowCheckTestCase, self).setUp()
        ovs_conf.register_ovs_agent_opts(cfg=cfg.CONF)
        cfg.CONF.set_override('extensions', ['service_datapath'], 'agent')

        self.context = context.get_admin_context()

        self.ext = importutils.import_object(_EXT_CLASS)
        self.agent_api = mock.Mock()
        self.agent_api.bridge_mappings = {"test": "br-test"}
        self.agent_api.phys_ofports = {"test": 1}
        self.agent_api.plugin_rpc.get_privatefloating_info.return_value = {
            'privatefloating_network': {
                'provider:physical_network': 'test',
                'provider:segmentation_id': 100,
                'subnets_detail': [{
                    'host_routes': [
                        {'nexthop': '*************',
                         'destination': '***********/24'}
                    ],
                    'id': _uuid(),
                    'cidr': '***********/24',
                }]
            },
            'privatefloating_port': {
                'mac_address': 'fa:16:3e:00:00:01',
                'fixed_ips': [
                    {'ip_address': '*************'}]
            }
        }
        self.ext.init_loop = mock.Mock()
        self.ext.consume_api(self.agent_api)

        with mock.patch('ryu.base.app_manager.AppManager.get_instance') \
                as get_instance:
            with mock.patch(_OVS_BRIDGE_MOD + '.OVSIntegrationBridge') \
                    as br_int:
                br_int.get_port_ofport = mock.Mock(return_value=2)
                with mock.patch(_OVS_BRIDGE_MOD + '.OVSPhysicalBridge'):
                    get_instance.instantiate.return_value = mock.Mock()
                    self.ext.initialize(None, None)
                    self.ext.init_check_manager()

        self.sdp_ext = self.ext.ext_check_mgr.extensions[0]

    def test_service_datapath_flow_check(self):
        result_map = collections.defaultdict(plugin.PortCheckResult)
        port = port_obj.Port(id=_uuid(),
                             network_id=_uuid(),
                             device_owner='compute:fake',
                             mac_address=netaddr.EUI('64:16:7F:68:75:A1'),
                             fixed_ips=[port_obj.IPAllocation(
                                 subnet_id=_uuid(), ip_address='*******')])

        self.sdp_ext.phy_br._get_dp.return_value = [
            mock.Mock(), mock.Mock(), mock.Mock()]

        self.sdp_ext.ndp_direct_ip6 = mock.Mock()
        self.sdp_ext.ingress_direct_ip6 = mock.Mock()
        self.sdp_ext.send_arp_to_route_destination = mock.Mock()
        self.sdp_ext.init_path_flows = mock.Mock()
        self.sdp_ext.init_service_path_base_pipeline_flows = mock.Mock()
        self.sdp_ext.process_install_service_path_flows = mock.Mock()
        self.sdp_ext.install_destination_mac_change_flows = mock.Mock()

        self.ext.ext_check_mgr.do_extensions_flow_check(
            context, result_map, [port])

        self.sdp_ext.phy_br.arp_direct.assert_called_with(100)
        self.sdp_ext.phy_br.ingress_direct.assert_called_with(100)
        self.sdp_ext.ndp_direct_ip6.assert_called_with(100)
        self.sdp_ext.ingress_direct_ip6.assert_called_with(100)
        self.sdp_ext.send_arp_to_route_destination.assert_called()
        self.sdp_ext.init_path_flows.assert_called()
        self.sdp_ext.init_service_path_base_pipeline_flows.assert_called()
        self.sdp_ext.process_install_service_path_flows.assert_called()
        self.sdp_ext.install_destination_mac_change_flows.assert_called_with(
            '*************', None)
