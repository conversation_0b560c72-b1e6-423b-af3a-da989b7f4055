#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.callbacks import registry
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from oslo_log import log as logging

from neutron._i18n import _
from neutron.api.rpc.agentnotifiers import l3_rpc_agent_api
from neutron.db import _resource_extend as resource_extend
from neutron.extensions import router_check as ext_router_check

LOG = logging.getLogger(__name__)


class RouterCheckParamsConflict(n_exc.Conflict):
    message = _("router_id and host cannot "
                "be specified at the same time")


@resource_extend.has_resource_extenders
@registry.has_registry_receivers
class RouterCheckPlugin(ext_router_check.RouterCheckPluginBase):
    supported_extension_aliases = ['router-check']

    def __init__(self):
        super(RouterCheckPlugin, self).__init__()
        self.l3_plugin = directory.get_plugin(constants.L3)
        self._l3_rpc_notifier = l3_rpc_agent_api.L3AgentNotifyAPI()

    def router_check(self, context, router_id=None, host=None):
        LOG.info('Getting router check info')
        if not router_id and not host:
            raise n_exc.BadRequest(resource='router_check',
                                   msg=_("router_id or host needed"))
        if router_id and host:
            raise RouterCheckParamsConflict()
        result = []
        if router_id:
            result = self.routers_check(context, [router_id], host)
        if host:
            router_ids = self._get_routers_by_host(context, host)
            result = self.routers_check(context, router_ids, host)
        return result

    def routers_check(self, context, router_ids, host):
        routers = []
        for router_id in router_ids:
            router = self.l3_plugin._get_router(context, router_id)
            routers.append(router)

        if len(routers) == 0:
            raise n_exc.BadRequest(resource='router_check',
                                   msg=_("No router found"))

        if host is None:
            agents = self.l3_plugin.list_l3_agents_hosting_router(
                context, router_ids[0])['agents']
            if len(agents) == 0 or agents[0].get('host') is None:
                raise n_exc.BadRequest(
                    resource='router_check',
                    msg=_(
                        "No l3 agent found for router %s"
                    ) % router_ids[0])
            host = agents[0]['host']
            if routers[0].extra_attributes.ha:
                host = self.l3_plugin.get_active_host_for_ha_router(
                    context, router_ids[0])

        # Check the HA status and obtain the
        # router that is active on the host.
        ha_state_result, active_router_ids = (
            self.check_routers_ha_state(context, routers, host))

        results = self._l3_rpc_notifier.routers_check(
            context, active_router_ids, host)

        diff_router_ids = set(router_ids) - set(active_router_ids)

        for router_id in diff_router_ids:
            ha_state_check = {'ha_state': ha_state_result.get(router_id)}

            result = {}
            result['router_id'] = router_id
            result['check result'] = ha_state_check
            result['check result']['Remind'] = \
                ("This router is a backup router on this "
                 "host or it is not bound to the host.")
            results['router_check'].append(result)

        for result in results['router_check']:
            id = result['router_id']
            result['check result']['ha_state'] = ha_state_result[id]

        return results

    def router_ns_info(self, context, router_id):
        router = self.l3_plugin._get_router(context, router_id)
        agents = self.l3_plugin.list_l3_agents_hosting_router(
            context, router_id)['agents']
        if len(agents) == 0 or agents[0].get('host') is None:
            raise n_exc.BadRequest(resource='router_check',
                                   msg=_(
                                       "No l3 agent found for router %s"
                                   ) % router_id)
        host = agents[0]['host']
        if router.extra_attributes.ha:
            host = self.l3_plugin.get_active_host_for_ha_router(
                context, router_id)

        info = self._l3_rpc_notifier.router_ns_info(
            context, host, router_id)
        return info

    def check_routers_ha_state(self, context, routers, host):
        # Initialize the result dictionary
        # and list of active router IDs.
        ha_state_result = {}
        active_router_ids = []

        for router in routers:
            result = {"status": True,
                      "ha_state_error": ''}
            router_id = router['id']

            # If the router is not highly available,
            # add it to the result dictionary and
            # active router ID list, and then skip this loop.
            if router['extra_attributes']['ha'] is False:
                ha_state_result[router_id] = result
                active_router_ids.append(router_id)
                continue

            agents = self.l3_plugin.list_l3_agents_hosting_router(
                context, router_id)
            if len(agents['agents']) <= 0:
                self._ha_error_message(result,
                                       'No l3 agent found.',
                                       ha_state_result,
                                       router_id)
                continue

            # If the HA router is the master router on the host,
            # add it to the list of active router IDs.
            active_host = self.l3_plugin.get_active_host_for_ha_router(
                context, router_id)
            if active_host is not None and host == active_host:
                active_router_ids.append(router_id)

            # Check the HA state of the router.
            if len(agents['agents']) < 2:
                if agents['agents'][0]['ha_state'] == 'active':
                    self._ha_error_message(result,
                                           'No backup router.',
                                           ha_state_result,
                                           router_id)
                else:
                    self._ha_error_message(result,
                                           'No master router.',
                                           ha_state_result,
                                           router_id)
                continue

            # At present, the router only exists on two agents.
            ha_state1 = agents['agents'][0]['ha_state']
            ha_state2 = agents['agents'][1]['ha_state']
            if ha_state1 == 'active' and ha_state2 == 'active':
                self._ha_error_message(result, 'Dual master router.',
                                       ha_state_result, router_id)
                if router_id not in active_router_ids:
                    active_router_ids.append(router_id)
            elif ha_state1 == 'standby' and ha_state2 == 'standby':
                self._ha_error_message(result, 'Dual backup router.',
                                       ha_state_result, router_id)
            elif ha_state1 == 'unknown' or ha_state2 == 'unknown':
                self._ha_error_message(result, 'Unknown ha state.',
                                       ha_state_result, router_id)
            else:
                ha_state_result[router_id] = result

        return ha_state_result, active_router_ids

    def _ha_error_message(self, result, message,
                          ha_state_result, router_id):
        if message != '':
            result["status"] = False
            result["ha_state_error"] = message
            ha_state_result[router_id] = result

    def _get_routers_by_host(self, context, host):
        router_ids = self.l3_plugin.list_router_ids_on_host(
            context, host)
        return router_ids
