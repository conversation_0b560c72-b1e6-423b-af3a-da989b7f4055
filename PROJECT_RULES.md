# Neutron 项目架构与开发规范

## 1. 项目架构概述

本项目基于 OpenStack Neutron 架构，采用分层设计模式，主要包含以下核心组件：

- **API Layer**: RESTful API 接口层
- **Plugin Layer**: 核心业务逻辑插件层
- **Extension Layer**: 功能扩展层
- **Model Layer**: 数据模型层
- **Agent Layer**: 分布式代理层
- **Service Layer**: 服务管理层

## 2. Server 端架构规范

### 2.1 API 层开发规范

#### 2.1.1 Controller 实现

**位置**: `neutron/api/v2/base.py`

**核心类**: `Controller`

**标准操作**:
```python
class Controller(object):
    LIST = 'list'      # GET /resources
    SHOW = 'show'      # GET /resources/{id}
    CREATE = 'create'  # POST /resources
    UPDATE = 'update'  # PUT /resources/{id}
    DELETE = 'delete'  # DELETE /resources/{id}
```

**实现要求**:
- 继承 `neutron.api.v2.base.Controller`
- 实现标准 CRUD 操作
- 使用 `neutron.api.v2.resource.Resource` 包装控制器
- 支持批量操作和过滤查询
- 实现适当的权限检查和配额限制

#### 2.1.2 资源定义规范

**位置**: `neutron/api/v2/resource.py`

**实现模式**:
```python
def Resource(controller, faults=None, deserializers=None, 
             serializers=None, action_status=None):
    # 资源序列化/反序列化逻辑
    # 默认支持 JSON 格式
    # 自定义状态码映射
```

### 2.2 Plugin 层开发规范

#### 2.2.1 核心 Plugin 实现

**基类**: `neutron.neutron_plugin_base_v2.NeutronPluginBaseV2`

**必须实现的抽象方法**:
```python
@abc.abstractmethod
def create_subnet(self, context, subnet):
    pass

@abc.abstractmethod  
def update_subnet(self, context, id, subnet):
    pass

@abc.abstractmethod
def get_subnet(self, context, id, fields=None):
    pass

@abc.abstractmethod
def get_subnets(self, context, filters=None, fields=None):
    pass

@abc.abstractmethod
def delete_subnet(self, context, id):
    pass
```

#### 2.2.2 ML2 Plugin 架构

**位置**: `neutron/plugins/ml2/plugin.py`

**核心特性**:
- **Type Drivers**: 网络类型驱动（VLAN, VXLAN, GRE等）
- **Mechanism Drivers**: 机制驱动（OVS, LinuxBridge等）
- **Extension Drivers**: 扩展驱动（Port Security, QoS等）

**实现规范**:
```python
class Ml2Plugin(db_base_plugin_v2.NeutronDbPluginV2,
                dvr_mac_db.DVRDbMixin,
                external_net_db.External_net_db_mixin,
                # 其他 Mixin 类
                ):
    
    def __init__(self):
        # 初始化 Type Manager
        # 初始化 Mechanism Manager  
        # 初始化 Extension Manager
```

### 2.3 Extension 层开发规范

#### 2.3.1 Extension 定义

**位置**: `neutron/extensions/`

**基类**: `neutron_lib.api.extensions.APIExtensionDescriptor`

**实现模板**:
```python
class L3(extensions.APIExtensionDescriptor):
    api_definition = l3_apidef
    
    @classmethod
    def get_resources(cls):
        """返回扩展资源"""
        plural_mappings = resource_helper.build_plural_mappings(
            {}, l3_apidef.RESOURCE_ATTRIBUTE_MAP)
        return resource_helper.build_resource_info(
            plural_mappings, l3_apidef.RESOURCE_ATTRIBUTE_MAP,
            constants.L3, action_map=l3_apidef.ACTION_MAP,
            register_quota=True)
```

#### 2.3.2 Extension 开发要求

- **API 定义**: 使用 `neutron_lib.api.definitions` 定义 API
- **资源映射**: 定义 `RESOURCE_ATTRIBUTE_MAP`
- **动作映射**: 定义 `ACTION_MAP`
- **配额注册**: 支持配额管理
- **权限控制**: 集成 RBAC 权限控制

### 2.4 Model 层开发规范

#### 2.4.1 数据模型定义

**位置**: `neutron/db/models_v2.py`

**基类**: `neutron_lib.db.model_base.BASEV2`

**标准模型结构**:
```python
class Network(standard_attr.HasStandardAttributes,
              model_base.BASEV2, model_base.HasId,
              model_base.HasProject):
    """网络模型"""
    name = sa.Column(sa.String(db_const.NAME_FIELD_SIZE))
    status = sa.Column(sa.String(16), nullable=False)
    admin_state_up = sa.Column(sa.Boolean(), nullable=False)
    
class Subnet(standard_attr.HasStandardAttributes,
             model_base.BASEV2, model_base.HasId,
             model_base.HasProject):
    """子网模型"""
    network_id = sa.Column(sa.String(36), 
                          sa.ForeignKey('networks.id', ondelete="CASCADE"),
                          nullable=False)
    cidr = sa.Column(sa.String(64), nullable=False)
```

#### 2.4.2 模型设计原则

- **继承标准基类**: 使用 `BASEV2`, `HasId`, `HasProject` 等
- **标准属性**: 继承 `HasStandardAttributes` 支持标准属性
- **外键约束**: 正确设置外键关系和级联删除
- **索引优化**: 为查询字段添加适当索引
- **数据验证**: 在模型层进行基础数据验证

#### 2.4.3 数据库迁移

**位置**: `neutron/db/migration/alembic_migrations/versions/`

**迁移脚本规范**:
```python
def upgrade():
    op.create_table(
        'new_table',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('name', sa.String(255)),
        # 其他字段定义
    )
    
def downgrade():
    op.drop_table('new_table')
```

## 3. Agent 端架构规范

### 3.1 Agent 基础架构

#### 3.1.1 Agent 服务结构

**入口文件**: `neutron/agent/l3_agent.py`

**服务启动模式**:
```python
def main(manager='neutron.agent.l3.agent.L3NATAgentWithStateReport',
         bridge_class=None):
    server = neutron_service.Service.create(
        binary='neutron-l3-agent',
        topic=topics.L3_AGENT,
        report_interval=cfg.CONF.AGENT.report_interval,
        manager=manager,
        bridge_class=bridge_class)
    service.launch(cfg.CONF, server, restart_method='mutate').wait()
```

#### 3.1.2 Agent Manager 实现

**位置**: `neutron/agent/l3/agent.py`

**核心类**: `L3NATAgentWithStateReport`

**关键组件**:
- **RPC 客户端**: 与 Server 端通信
- **资源处理队列**: 异步处理资源更新
- **周期性任务**: 状态同步和健康检查
- **扩展管理器**: 加载和管理 Agent 扩展

### 3.2 Agent 开发规范

#### 3.2.1 RPC 通信规范

**RPC 客户端实现**:
```python
class L3PluginApi(object):
    def __init__(self, topic, host):
        self.host = host
        target = oslo_messaging.Target(
            topic=topic, version='1.0')
        self.client = n_rpc.get_client(target)
    
    def get_routers(self, context, router_ids=None):
        cctxt = self.client.prepare()
        return cctxt.call(context, 'get_routers', 
                         host=self.host, router_ids=router_ids)
```

**RPC 服务端实现**:
```python
class L3RpcCallback(object):
    target = oslo_messaging.Target(version='1.0')
    
    def get_routers(self, context, host, router_ids=None):
        # 处理 RPC 调用逻辑
        return routers
```

#### 3.2.2 资源处理模式

**队列处理机制**:
```python
class ResourceProcessingQueue(object):
    def __init__(self):
        self._queue = queue.PriorityQueue()
    
    def add(self, update, priority=PRIORITY_RPC):
        self._queue.put((priority, update))
    
    def each_update_to_next_resource(self):
        # 按优先级处理资源更新
```

**优先级定义**:
- `PRIORITY_RELATED_ROUTER = 0` (最高优先级)
- `PRIORITY_RPC = 1`
- `PRIORITY_SYNC_ROUTERS_TASK = 2`
- `PRIORITY_PD_UPDATE = 3` (最低优先级)

#### 3.2.3 周期性任务

**状态报告**:
```python
@periodic_task.periodic_task(spacing=CONF.AGENT.report_interval)
def _report_state(self, context):
    # 向 Server 报告 Agent 状态
    self.state_rpc.report_state(context, self.agent_state)
```

**资源同步**:
```python
@periodic_task.periodic_task
def periodic_sync_routers_task(self, context):
    # 定期同步路由器状态
    self.sync_routers_task(context)
```

### 3.3 Agent 扩展开发

#### 3.3.1 扩展接口定义

**基类**: `neutron.agent.l3.l3_agent_extension_api.L3AgentExtensionAPI`

**扩展实现**:
```python
class MyL3AgentExtension(object):
    def initialize(self, connection, driver_type):
        # 初始化扩展
        pass
    
    def add_router(self, context, data):
        # 处理路由器添加
        pass
    
    def update_router(self, context, data):
        # 处理路由器更新  
        pass
    
    def delete_router(self, context, data):
        # 处理路由器删除
        pass
```

#### 3.3.2 扩展管理器

**位置**: `neutron/agent/l3/l3_agent_extensions_manager.py`

**加载机制**:
```python
class L3AgentExtensionsManager(object):
    def __init__(self, conf):
        self.extensions = []
        self._load_all_extensions()
    
    def _load_all_extensions(self):
        # 从配置加载所有扩展
        for extension_name in self.conf.agent.extensions:
            extension = self._load_extension(extension_name)
            self.extensions.append(extension)
```

## 4. 代码质量规范

### 4.1 编码标准

- **PEP 8**: 严格遵循 Python 编码规范
- **OpenStack Style**: 遵循 OpenStack 编码风格
- **类型注解**: 推荐使用类型注解提高代码可读性
- **文档字符串**: 所有公共方法必须有详细的文档字符串

### 4.2 特定规范检查

- [N322] 正确使用 `assert_called_once_with`
- [N328] 正确使用 `assertEqual`
- [N330] 使用 `assertEqual(*empty*, observed)` 格式
- [N331] 避免错误使用 `assertTrue(isinstance())`
- [N332] HTTP 状态码比较格式规范
- [N334] 统一使用 unittest2
- [N340] 检查 i18n 模块使用
- [N341] 检查内置 `_` 函数使用
- [N343] 生产代码不得导入 `neutron.tests.*`
- [N344] Python 3: 使用列表推导式替代 `filter(lambda)`

## 5. 服务层架构规范

### 5.1 Service Manager

**位置**: `neutron/service.py`

**核心类**: `WsgiService`, `RpcWorker`

**服务启动流程**:
```python
class WsgiService(object):
    def __init__(self, app, port, host='0.0.0.0'):
        self.app = app
        self.port = port
        self.host = host
        
    def start(self):
        # 启动 WSGI 服务
        pass
```

### 5.2 RPC 服务架构

**位置**: `neutron/common/rpc.py`

**RPC 初始化**:
```python
def init(conf, rpc_ext_mods=None):
    global TRANSPORT, NOTIFICATION_TRANSPORT, NOTIFIER
    # 初始化 RPC 传输层
    # 初始化通知传输层
    # 初始化通知器
```

**RPC 客户端模式**:
```python
def get_client(target, version_cap=None, serializer=None):
    return oslo_messaging.RPCClient(
        TRANSPORT, target, version_cap=version_cap,
        serializer=serializer)
```

### 5.3 Plugin Manager

**位置**: `neutron/manager.py`

**插件管理**:
```python
class Manager(periodic_task.PeriodicTasks):
    target = oslo_messaging.Target(version='1.0')
    
    def __init__(self, host=None):
        # 初始化插件管理器
        # 加载核心插件
        # 加载服务插件
```

## 6. 开发环境配置

### 6.1 Python 版本支持

- **主要支持**: Python 2.7, Python 3.5+
- **推荐开发环境**: Python 3.x
- **兼容性**: 代码必须同时支持 Python 2.7 和 Python 3.x

### 6.2 依赖管理

**依赖文件结构**:
- **requirements.txt**: 生产环境依赖
- **test-requirements.txt**: 测试环境依赖
- **upper-constraints.txt**: 依赖版本上限约束
- **lower-constraints.txt**: 依赖版本下限约束

**特殊依赖说明**:
```bash
# neutron-lib 使用内部开发版本
# git@************:openstack_r_group/neutron-lib.git#egg=neutron-lib
# 通过 tools/install_wo_neutron_lib.sh 脚本安装
```

### 6.3 开发工具配置

**虚拟环境管理**:
```bash
# 使用 tox 管理虚拟环境
tox -e py35  # Python 3.5 环境
tox -e py27  # Python 2.7 环境
tox -e pep8  # 代码风格检查
```

**包管理工具**:
- **pbr**: 用于包版本管理和构建
- **setuptools**: 包安装和分发
- **pip**: 依赖包安装

**配置管理**:
- **oslo.config**: 统一配置管理框架
- **配置文件**: etc/ 目录下的各种配置模板

## 7. 测试规范

### 7.1 测试架构

**测试目录结构**:
```
neutron/tests/
├── unit/           # 单元测试
├── functional/     # 功能测试
├── fullstack/      # 全栈测试
├── tempest/        # 集成测试
└── base.py         # 测试基类
```

### 7.2 测试类型

#### 7.2.1 单元测试
- **目标**: 测试单个函数或方法
- **隔离性**: 使用 mock 隔离外部依赖
- **覆盖率**: 新代码覆盖率不低于 80%
- **基类**: 继承 `neutron.tests.base.BaseTestCase`

#### 7.2.2 功能测试
- **目标**: 测试组件间交互
- **环境**: 使用真实的数据库和网络
- **范围**: API 层到数据库层的完整流程

#### 7.2.3 全栈测试
- **目标**: 端到端功能验证
- **环境**: 启动完整的 Neutron 服务
- **场景**: 模拟真实的网络操作场景

### 7.3 测试实现规范

#### 7.3.1 测试基类使用
```python
from neutron.tests import base

class TestMyFeature(base.BaseTestCase):
    def setUp(self):
        super(TestMyFeature, self).setUp()
        # 测试初始化代码
    
    def test_create_resource(self):
        # 测试用例实现
        pass
```

#### 7.3.2 Mock 使用规范
```python
import mock

class TestPlugin(base.BaseTestCase):
    def setUp(self):
        super(TestPlugin, self).setUp()
        self.plugin = mock.Mock()
        
    def test_method_call(self):
        self.plugin.create_network.return_value = {'id': 'test-id'}
        result = self.plugin.create_network({})
        self.plugin.create_network.assert_called_once_with({})
```

#### 7.3.3 数据库测试
```python
from neutron.tests.unit import testlib_api

class TestDbOperations(testlib_api.SqlTestCase):
    def test_create_network(self):
        with self.session.begin():
            # 数据库操作测试
            pass
```

### 7.4 测试命令

```bash
# 运行所有测试
tox

# 运行特定环境测试
tox -e py35          # Python 3.5 单元测试
tox -e py27          # Python 2.7 单元测试
tox -e pep8          # 代码风格检查
tox -e functional    # 功能测试
tox -e fullstack     # 全栈测试
tox -e api           # API 测试
tox -e cover         # 代码覆盖率

# 运行特定测试
stestr run neutron.tests.unit.test_manager
stestr run neutron.tests.functional.agent.test_l3_agent
```

### 7.5 测试数据管理

#### 7.5.1 测试数据隔离
- 每个测试用例使用独立的数据库事务
- 测试结束后自动回滚数据变更
- 使用 fixture 管理测试数据生命周期

#### 7.5.2 测试配置
```python
# 测试配置覆盖
self.config(core_plugin='neutron.plugins.ml2.plugin.Ml2Plugin')
self.config(service_plugins=['router'])
```

## 8. 代码提交规范

### 8.1 Git 工作流

#### 8.1.1 分支策略
- **主分支**: master 分支为稳定分支
- **开发分支**: 基于 master 创建功能分支
- **命名规范**: feature/功能名称, bugfix/问题描述
- **合并策略**: 使用 Gerrit 进行代码审查后合并

#### 8.1.2 Gerrit 工作流
```bash
# 配置 git review
git review -s

# 提交代码审查
git add .
git commit -m "实现新功能: 添加网络QoS支持"
git review

# 修改后重新提交
git commit --amend
git review
```

### 8.2 提交信息规范

#### 8.2.1 提交信息格式
```
<type>: <subject>

<body>

Closes-Bug: #1234567
Implements: blueprint network-qos
Change-Id: I1234567890abcdef1234567890abcdef12345678
```

#### 8.2.2 类型说明
- **feat**: 新功能实现
- **fix**: Bug 修复
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **perf**: 性能优化
- **chore**: 构建或工具变动

### 8.3 代码审查要求

#### 8.3.1 审查流程
- **提交审查**: 通过 Gerrit 提交代码审查
- **自动检查**: 必须通过 CI/CD 自动化检查
- **人工审查**: 至少需要一个 +2 审查通过
- **合并条件**: 通过所有检查且获得审查通过

#### 8.3.2 审查标准
- **功能正确性**: 实现符合需求规范
- **代码质量**: 遵循编码规范和最佳实践
- **测试覆盖**: 包含充分的测试用例
- **文档完整**: API 变更需要更新文档
- **向后兼容**: 不破坏现有 API 兼容性

## 6. 文档规范

### 6.1 代码文档

- **模块文档**: 每个模块必须有清晰的文档字符串
- **函数文档**: 公共函数必须有详细的文档字符串
- **类文档**: 类和方法必须有适当的文档
- **格式标准**: 使用 reStructuredText 格式

### 6.2 项目文档

- **API 文档**: api-ref/ 目录下维护 API 文档
- **开发文档**: doc/ 目录下维护开发文档
- **发布说明**: releasenotes/ 目录下维护发布说明
- **示例代码**: examples/ 目录下提供使用示例

## 7. 安全规范

### 7.1 安全检查

- **bandit 扫描**: 定期运行 bandit 安全扫描
- **依赖检查**: 定期检查依赖包安全漏洞
- **敏感信息**: 禁止在代码中硬编码敏感信息

### 7.2 安全最佳实践

- **输入验证**: 所有外部输入必须验证
- **权限控制**: 实施最小权限原则
- **日志安全**: 避免在日志中记录敏感信息
- **加密传输**: 敏感数据传输必须加密

## 8. 性能规范

### 8.1 性能要求

- **响应时间**: API 响应时间不超过 2 秒
- **并发处理**: 支持高并发请求处理
- **资源使用**: 合理使用内存和 CPU 资源
- **数据库优化**: 优化数据库查询性能

### 8.2 性能监控

- **性能测试**: 定期进行性能测试
- **监控指标**: 监控关键性能指标
- **性能分析**: 使用性能分析工具优化代码

## 9. 部署规范

### 9.1 部署架构

#### 9.1.1 服务组件
- **neutron-server**: API 服务器，处理 REST API 请求
- **neutron-dhcp-agent**: DHCP 代理，管理 DHCP 服务
- **neutron-l3-agent**: L3 代理，处理路由和 NAT
- **neutron-metadata-agent**: 元数据代理
- **neutron-openvswitch-agent**: Open vSwitch 代理

#### 9.1.2 配置文件
- **主配置**: `/etc/neutron/neutron.conf`
- **插件配置**: `/etc/neutron/plugins/ml2/ml2_conf.ini`
- **代理配置**: `/etc/neutron/dhcp_agent.ini`, `/etc/neutron/l3_agent.ini`

### 9.2 配置管理

#### 9.2.1 配置规范
```ini
# neutron.conf 示例
[DEFAULT]
core_plugin = ml2
service_plugins = router
allow_overlapping_ips = True

[database]
connection = mysql+pymysql://neutron:password@controller/neutron

[keystone_authtoken]
auth_url = http://controller:5000
memcached_servers = controller:11211
auth_type = password
project_domain_name = default
user_domain_name = default
project_name = service
username = neutron
password = NEUTRON_PASS
```

#### 9.2.2 环境变量
- **OS_AUTH_URL**: Keystone 认证地址
- **OS_PROJECT_NAME**: 项目名称
- **OS_USERNAME**: 用户名
- **OS_PASSWORD**: 密码

## 10. 最佳实践

### 10.1 代码组织

#### 10.1.1 模块划分
- **按功能划分**: 每个功能模块独立目录
- **分层架构**: API -> Service -> DB 分层清晰
- **接口抽象**: 定义清晰的接口和抽象类

#### 10.1.2 依赖管理
- **最小依赖**: 只引入必要的依赖
- **版本锁定**: 在 requirements.txt 中锁定版本
- **循环依赖**: 避免模块间循环依赖

### 10.2 错误处理

#### 10.2.1 异常处理
```python
# 标准异常处理模式
try:
    result = some_operation()
except SpecificException as e:
    LOG.error("操作失败: %s", e)
    raise NeutronException(_("操作失败: %s") % e)
except Exception as e:
    LOG.exception("未预期的错误")
    raise
```

#### 10.2.2 日志记录
- **日志级别**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **日志格式**: 包含时间戳、级别、模块、消息
- **敏感信息**: 不在日志中记录密码等敏感信息

### 10.3 性能优化

#### 10.3.1 数据库优化
- **索引使用**: 为查询字段添加适当索引
- **批量操作**: 使用批量插入/更新减少数据库访问
- **连接池**: 合理配置数据库连接池

#### 10.3.2 缓存策略
- **内存缓存**: 使用 dogpile.cache 进行内存缓存
- **分布式缓存**: 使用 Redis/Memcached 进行分布式缓存
- **缓存失效**: 合理设置缓存过期时间

## 11. 项目工具

### 11.1 开发工具

#### 11.1.1 代码检查工具
```bash
# 代码风格检查
tox -e pep8

# 安全检查
tox -e bandit

# 类型检查
tox -e mypy

# 代码复杂度检查
tox -e complexity
```

#### 11.1.2 测试工具
```bash
# 单元测试
tox -e py39

# 功能测试
tox -e functional

# 集成测试
tox -e fullstack

# 覆盖率报告
tox -e cover
```

### 11.2 构建工具

#### 11.2.1 包管理
- **pbr**: 自动化包构建和版本管理
- **setuptools**: Python 包构建工具
- **pip**: 依赖包安装工具

#### 11.2.2 文档构建
```bash
# 构建 API 文档
tox -e api-ref

# 构建开发者文档
tox -e docs

# 构建发布说明
tox -e releasenotes
```

### 11.3 CI/CD 工具

#### 11.3.1 持续集成
- **Zuul**: OpenStack 官方 CI 系统
- **Jenkins**: 可选的 CI 工具
- **GitHub Actions**: 社区版本可用

#### 11.3.2 代码质量
- **SonarQube**: 代码质量分析
- **CodeClimate**: 代码质量评估
- **Codecov**: 测试覆盖率报告

## 12. 社区贡献

### 12.1 贡献流程

#### 12.1.1 准备工作
1. **注册账号**: 在 Launchpad 注册账号
2. **签署 CLA**: 签署贡献者许可协议
3. **配置环境**: 设置开发环境和 git review

#### 12.1.2 贡献步骤
1. **创建 Blueprint**: 对于新功能，先创建 Blueprint
2. **报告 Bug**: 在 Launchpad 报告发现的问题
3. **编写代码**: 按照规范编写代码
4. **提交审查**: 通过 Gerrit 提交代码审查
5. **响应反馈**: 及时响应审查者的反馈

### 12.2 社区规范

#### 12.2.1 沟通规范
- **邮件列表**: 使用官方邮件列表讨论
- **IRC 频道**: #openstack-neutron
- **会议参与**: 参加定期的社区会议

#### 12.2.2 行为准则
- **尊重他人**: 保持友好和专业的态度
- **建设性反馈**: 提供有建设性的意见和建议
- **及时响应**: 及时回应社区的询问和反馈

---

## 总结

本文档详细描述了 OpenStack Neutron 项目的代码架构和开发规范，涵盖了从服务端 API、Plugin 实现到 Agent 端开发的完整指南。遵循这些规范可以确保代码质量、提高开发效率，并促进团队协作。

**关键要点**:
1. **分层架构**: API -> Service -> Plugin -> Agent 的清晰分层
2. **扩展机制**: 通过 Extension 和 Plugin 实现功能扩展
3. **标准化**: 遵循 OpenStack 和 PEP 8 编码规范
4. **测试驱动**: 完善的测试体系保证代码质量
5. **社区协作**: 积极参与 OpenStack 社区贡献

## 11. 错误处理

### 11.1 异常定义

- **自定义异常**: 继承自 `NeutronException`
- **错误码**: 使用标准 HTTP 状态码
- **错误消息**: 提供清晰的错误描述

### 11.2 错误响应

- **统一格式**: 所有错误响应使用统一格式
- **详细信息**: 包含足够的调试信息
- **用户友好**: 错误消息对用户友好

---

**注意**: 本规则文档应定期更新，以反映项目的最新要求和最佳实践。所有团队成员都应熟悉并遵循这些规则。

**联系方式**: 如有疑问，请通过邮件列表 <EMAIL> 联系，邮件主题请使用 [Neutron] 标签。

**参考资源**:
- [OpenStack 开发文档](https://docs.openstack.org)
- [Neutron 开发规范](http://wiki.wocloud.design/pages/viewpage.action?pageId=4362528)
- [OpenStack Style Commandments](https://docs.openstack.org/hacking/latest/)