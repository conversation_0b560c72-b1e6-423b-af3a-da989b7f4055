#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add subnet dhcp ips

Revision ID: ad4490c8355b
Revises: 86104f671675
Create Date: 2024-11-05 18:43:28.380941

"""

from alembic import op
from neutron_lib.db import constants as db_const
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ad4490c8355b'
down_revision = '86104f671675'


def upgrade():
    exist = False

    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'subnet_dhcp_ips':
            exist = True
            break

    if not exist:
        op.create_table(
            'subnet_dhcp_ips',
            sa.Column('ip_address',
                      sa.String(length=db_const.IP_ADDR_FIELD_SIZE),
                      primary_key=True),
            sa.Column('subnet_id',
                      sa.String(length=db_const.UUID_FIELD_SIZE),
                      primary_key=True),
            sa.ForeignKeyConstraint(['subnet_id'], ['subnets.id'],
                                    ondelete='CASCADE')
        )
