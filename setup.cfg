[metadata]
name = neutron
summary = OpenStack Networking
description-file =
    README.rst
author = OpenStack
author-email = <EMAIL>
home-page = https://docs.openstack.org/neutron/latest/
classifier =
    Environment :: OpenStack
    Intended Audience :: Information Technology
    Intended Audience :: System Administrators
    License :: OSI Approved :: Apache Software License
    Operating System :: POSIX :: Linux
    Programming Language :: Python
    Programming Language :: Python :: 2
    Programming Language :: Python :: 2.7
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.5

[files]
packages =
    neutron
data_files =
    etc/neutron =
        etc/api-paste.ini
        etc/policy.json
        etc/rootwrap.conf
    etc/neutron/rootwrap.d = etc/neutron/rootwrap.d/*
scripts =
    bin/neutron-rootwrap-xen-dom0
    tools/neutron-save-env.sh

[entry_points]
wsgi_scripts =
    neutron-api = neutron.server:get_application
console_scripts =
    neutron-db-manage = neutron.db.migration.cli:main
    neutron-debug = neutron.debug.shell:main
    neutron-dhcp-agent = neutron.cmd.eventlet.agents.dhcp:main
    neutron-keepalived-state-change = neutron.cmd.keepalived_state_change:main
    neutron-ipset-cleanup = neutron.cmd.ipset_cleanup:main
    neutron-l3-agent = neutron.cmd.eventlet.agents.l3:main
    neutron-linuxbridge-agent = neutron.cmd.eventlet.plugins.linuxbridge_neutron_agent:main
    neutron-linuxbridge-cleanup = neutron.cmd.linuxbridge_cleanup:main
    neutron-macvtap-agent = neutron.cmd.eventlet.plugins.macvtap_neutron_agent:main
    neutron-metadata-agent = neutron.cmd.eventlet.agents.metadata:main
    neutron-netns-cleanup = neutron.cmd.netns_cleanup:main
    neutron-openvswitch-agent = neutron.cmd.eventlet.plugins.ovs_neutron_agent:main
    neutron-ovs-cleanup = neutron.cmd.ovs_cleanup:main
    neutron-pd-notify = neutron.cmd.pd_notify:main
    neutron-server = neutron.cmd.eventlet.server:main
    neutron-rpc-server = neutron.cmd.eventlet.server:main_rpc_eventlet
    neutron-rootwrap = oslo_rootwrap.cmd:main
    neutron-rootwrap-daemon = oslo_rootwrap.cmd:daemon
    neutron-usage-audit = neutron.cmd.eventlet.usage_audit:main
    neutron-metering-agent = neutron.cmd.eventlet.services.metering_agent:main
    neutron-sriov-nic-agent = neutron.cmd.eventlet.plugins.sriov_nic_neutron_agent:main
    neutron-sanity-check = neutron.cmd.sanity_check:main
    neutron-port-check = neutron.cmd.check_port_service_path:main
    neutron-conntrack-check = neutron.cmd.conntrack_check:check_conntrack_main
neutron.core_plugins =
    ml2 = neutron.plugins.ml2.plugin:Ml2Plugin
neutron.service_plugins =
    dummy = neutron.tests.unit.dummy_plugin:DummyServicePlugin
    router = neutron.services.l3_router.l3_router_plugin:L3RouterPlugin
    metering = neutron.services.metering.metering_plugin:MeteringPlugin
    qos = neutron.services.qos.qos_plugin:QoSPlugin
    tag = neutron.services.tag.tag_plugin:TagPlugin
    flavors = neutron.services.flavors.flavors_plugin:FlavorsPlugin
    auto_allocate = neutron.services.auto_allocate.plugin:Plugin
    segments = neutron.services.segments.plugin:Plugin
    network_ip_availability = neutron.services.network_ip_availability.plugin:NetworkIPAvailabilityPlugin
    network_segment_range = neutron.services.network_segment_range.plugin:NetworkSegmentRangePlugin
    revisions = neutron.services.revisions.revision_plugin:RevisionPlugin
    timestamp = neutron.services.timestamp.timestamp_plugin:TimeStampPlugin
    trunk = neutron.services.trunk.plugin:TrunkPlugin
    loki = neutron.services.loki.loki_plugin:LokiPlugin
    log = neutron.services.logapi.logging_plugin:LoggingPlugin
    port_forwarding = neutron.services.portforwarding.pf_plugin:PortForwardingPlugin
    elastic_snat = neutron.services.elastic_snat.plugin:ElasticSnatPlugin
    traffic_mirror = neutron.services.traffic_mirror.plugin:TrafficMirrorPlugin
    port_check = neutron.services.port_check.plugin:PortCheckPlugin
    router_check = neutron.services.router_check.plugin:RouterCheckPlugin
    cloud_attribute = neutron.services.cloud_attribute.plugin:CloudAttributePlugin
    flow_log = neutron.services.flow_log.plugin:FlowLogPlugin
    route_table = neutron.services.route_table.plugin:RouteTablePlugin
    resources_notify = neutron.services.resources_notify.plugin:ResourcesNotifyPlugin
neutron.ml2.type_drivers =
    flat = neutron.plugins.ml2.drivers.type_flat:FlatTypeDriver
    local = neutron.plugins.ml2.drivers.type_local:LocalTypeDriver
    vlan = neutron.plugins.ml2.drivers.type_vlan:VlanTypeDriver
    geneve = neutron.plugins.ml2.drivers.type_geneve:GeneveTypeDriver
    gre = neutron.plugins.ml2.drivers.type_gre:GreTypeDriver
    vxlan = neutron.plugins.ml2.drivers.type_vxlan:VxlanTypeDriver
neutron.ml2.mechanism_drivers =
    logger = neutron.tests.unit.plugins.ml2.drivers.mechanism_logger:LoggerMechanismDriver
    test = neutron.tests.unit.plugins.ml2.drivers.mechanism_test:TestMechanismDriver
    test_with_agent = neutron.tests.unit.plugins.ml2.drivers.mechanism_test:TestMechanismDriverWithAgent
    linuxbridge = neutron.plugins.ml2.drivers.linuxbridge.mech_driver.mech_linuxbridge:LinuxbridgeMechanismDriver
    macvtap = neutron.plugins.ml2.drivers.macvtap.mech_driver.mech_macvtap:MacvtapMechanismDriver
    openvswitch = neutron.plugins.ml2.drivers.openvswitch.mech_driver.mech_openvswitch:OpenvswitchMechanismDriver
    l2population = neutron.plugins.ml2.drivers.l2pop.mech_driver:L2populationMechanismDriver
    sriovnicswitch = neutron.plugins.ml2.drivers.mech_sriov.mech_driver.mech_driver:SriovNicSwitchMechanismDriver
    fake_agent = neutron.tests.unit.plugins.ml2.drivers.mech_fake_agent:FakeAgentMechanismDriver
    faulty_agent = neutron.tests.unit.plugins.ml2.drivers.mech_faulty_agent:FaultyAgentMechanismDriver
    simulate_host = neutron.plugins.ml2.drivers.mech_simulate_host.mech_driver.mech_driver:SimulateHostMechanismDriver
neutron.ml2.extension_drivers =
    test = neutron.tests.unit.plugins.ml2.drivers.ext_test:TestExtensionDriver
    testdb = neutron.tests.unit.plugins.ml2.drivers.ext_test:TestDBExtensionDriver
    port_security = neutron.plugins.ml2.extensions.port_security:PortSecurityExtensionDriver
    qos = neutron.plugins.ml2.extensions.qos:QosExtensionDriver
    dns = neutron.plugins.ml2.extensions.dns_integration:DNSExtensionDriverML2
    data_plane_status = neutron.plugins.ml2.extensions.data_plane_status:DataPlaneStatusExtensionDriver
    dns_domain_ports = neutron.plugins.ml2.extensions.dns_integration:DNSDomainPortsExtensionDriver
    flow_log = neutron.plugins.ml2.extensions.flow_log:FlowLogExtensionDriver
neutron.ipam_drivers =
    fake = neutron.tests.unit.ipam.fake_driver:FakeDriver
    internal = neutron.ipam.drivers.neutrondb_ipam.driver:NeutronDbPool
neutron.agent.l2.extensions =
    qos = neutron.agent.l2.extensions.qos:QosAgentExtension
    fdb = neutron.agent.l2.extensions.fdb_population:FdbPopulationAgentExtension
    log = neutron.services.logapi.agent.log_extension:LoggingExtension
    dhcp = neutron.agent.l2.extensions.dhcp.extension:DHCPAgentExtension
    service_datapath = neutron.agent.l2.extensions.nat.service_datapath:ServiceDataPathAgentExtension
    metadata_path = neutron.agent.l2.extensions.nat.metadata_path:MetadataPathAgentExtension
    ra_speaker = neutron.agent.l2.extensions.ra_speaker:RASpeakerAgentExtension
    traffic_mirror = neutron.agent.l2.extensions.traffic_mirror.extension:TrafficMirrorAgentExtension
    port_check = neutron.agent.l2.extensions.port_check.extension:PortCheckAgentExtension
    lbaas_dscp = neutron.agent.l2.extensions.lbaas_dscp:LbaasDscpAgentExtension
neutron.agent.l3.extensions =
    fip_qos = neutron.agent.l3.extensions.qos.fip:FipQosAgentExtension
    port_forwarding = neutron.agent.l3.extensions.port_forwarding:PortForwardingAgentExtension
    fip_shared_qos = neutron.agent.l3.extensions.qos.fip_shared:SharedFipQosAgentExtension
    fip_bridge_qos = neutron.agent.l3.extensions.qos.fip_bridge:BridgeFipQosAgentExtension
    elastic_snat = neutron.agent.l3.extensions.elastic_snat:ElasticSnatAgentExtension
    fip_metering = neutron.agent.l3.extensions.fip_metering:FipMeteringAgentExtension
    route_table = neutron.agent.l3.extensions.route_table:RouteTableAgentExtension
neutron.services.logapi.drivers =
    ovs = neutron.services.logapi.drivers.openvswitch.ovs_firewall_log:OVSFirewallLoggingDriver
neutron.qos.agent_drivers =
    ovs = neutron.plugins.ml2.drivers.openvswitch.agent.extension_drivers.qos_driver:QosOVSAgentDriver
    sriov = neutron.plugins.ml2.drivers.mech_sriov.agent.extension_drivers.qos_driver:QosSRIOVAgentDriver
    linuxbridge = neutron.plugins.ml2.drivers.linuxbridge.agent.extension_drivers.qos_driver:QosLinuxbridgeAgentDriver
neutron.agent.linux.pd_drivers =
    dibbler = neutron.agent.linux.dibbler:PDDibbler
neutron.services.external_dns_drivers =
    designate = neutron.services.externaldns.drivers.designate.driver:Designate
oslo.config.opts =
    neutron = neutron.opts:list_opts
    neutron.agent = neutron.opts:list_agent_opts
    neutron.az.agent = neutron.opts:list_az_agent_opts
    neutron.base.agent = neutron.opts:list_base_agent_opts
    neutron.db = neutron.opts:list_db_opts
    neutron.dhcp.agent = neutron.opts:list_dhcp_agent_opts
    neutron.extensions = neutron.opts:list_extension_opts
    neutron.l3.agent = neutron.opts:list_l3_agent_opts
    neutron.metadata.agent = neutron.opts:list_metadata_agent_opts
    neutron.metering.agent = neutron.opts:list_metering_agent_opts
    neutron.ml2 = neutron.opts:list_ml2_conf_opts
    neutron.ml2.linuxbridge.agent = neutron.opts:list_linux_bridge_opts
    neutron.ml2.macvtap.agent = neutron.opts:list_macvtap_opts
    neutron.ml2.ovs.agent = neutron.opts:list_ovs_opts
    neutron.ml2.sriov.agent = neutron.opts:list_sriov_agent_opts
    neutron.ml2.xenapi = neutron.opts:list_xenapi_opts
    nova.auth = neutron.opts:list_auth_opts
oslo.config.opts.defaults =
    neutron = neutron.common.config:set_cors_middleware_defaults
oslo.policy.enforcer =
    neutron = neutron.policy:get_enforcer
neutron.db.alembic_migrations =
    neutron = neutron.db.migration:alembic_migrations
neutron.interface_drivers =
    linuxbridge = neutron.agent.linux.interface:BridgeInterfaceDriver
    null = neutron.agent.linux.interface:NullDriver
    openvswitch = neutron.agent.linux.interface:OVSInterfaceDriver
neutron.agent.firewall_drivers =
    noop = neutron.agent.firewall:NoopFirewallDriver
    iptables = neutron.agent.linux.iptables_firewall:IptablesFirewallDriver
    iptables_hybrid = neutron.agent.linux.iptables_firewall:OVSHybridIptablesFirewallDriver
    openvswitch = neutron.agent.linux.openvswitch_firewall:OVSFirewallDriver
    openvswitch_stateless = neutron.agent.linux.openvswitch_firewall:OVSStatelessFirewallDriver
neutron.services.metering_drivers =
    noop = neutron.services.metering.drivers.noop.noop_driver:NoopMeteringDriver
    iptables = neutron.services.metering.drivers.iptables.iptables_driver:IptablesMeteringDriver
    ovs = neutron.services.metering.drivers.openflow.ovs_driver:OvsMeteringDriver
neutron.objects =
    AddressScope = neutron.objects.address_scope:AddressScope
    AllowedAddressPair = neutron.objects.port.extensions.allowedaddresspairs:AllowedAddressPair
    Agent = neutron.objects.agent:Agent
    AutoAllocatedTopology = neutron.objects.auto_allocate:AutoAllocatedTopology
    PortDataPlaneStatus = neutron.objects.port.extensions.data_plane_status:PortDataPlaneStatus
    DefaultSecurityGroup = neutron.objects.securitygroup:DefaultSecurityGroup
    DistributedPortBinding = neutron.objects.ports:DistributedPortBinding
    DNSNameServer = neutron.objects.subnet:DNSNameServer
    DVRMacAddress = neutron.objects.router:DVRMacAddress
    ExternalNetwork = neutron.objects.network:ExternalNetwork
    ExtraDhcpOpt = neutron.objects.port.extensions.extra_dhcp_opt:ExtraDhcpOpt
    Flavor = neutron.objects.flavor:Flavor
    FlavorServiceProfileBinding = neutron.objects.flavor:FlavorServiceProfileBinding
    FloatingIP = neutron.objects.router:FloatingIP
    FloatingIPDNS = neutron.objects.floatingip:FloatingIPDNS
    FipADPortsLists = neutron.objects.floatingip:FipADPortsLists
    IPAllocation = neutron.objects.ports:IPAllocation
    IPAllocationPool = neutron.objects.subnet:IPAllocationPool
    IpamAllocation = neutron.objects.ipam:IpamAllocation
    IpamAllocationPool = neutron.objects.ipam:IpamAllocationPool
    IpamSubnet = neutron.objects.ipam:IpamSubnet
    Log = neutron.objects.logapi.logging_resource:Log
    L3HARouterAgentPortBinding = neutron.objects.l3_hamode:L3HARouterAgentPortBinding
    L3HARouterNetwork = neutron.objects.l3_hamode:L3HARouterNetwork
    L3HARouterVRIdAllocation = neutron.objects.l3_hamode:L3HARouterVRIdAllocation
    MeteringLabel = neutron.objects.metering:MeteringLabel
    MeteringLabelRule = neutron.objects.metering:MeteringLabelRule
    MeteringVpc = neutron.objects.metering:MeteringVpc
    Network = neutron.objects.network:Network
    NetworkDNSDomain = neutron.objects.network:NetworkDNSDomain
    NetworkDhcpAgentBinding = neutron.objects.network:NetworkDhcpAgentBinding
    NetworkPortSecurity = neutron.objects.network:NetworkPortSecurity
    NetworkRBAC = neutron.objects.network:NetworkRBAC
    NetworkSegment = neutron.objects.network:NetworkSegment
    NetworkSegmentRange = neutron.objects.network:NetworkSegmentRange
    Port = neutron.objects.ports:Port
    PortBinding = neutron.objects.ports:PortBinding
    PortBindingLevel = neutron.objects.ports:PortBindingLevel
    PortCloudAttribute = neutron.object.ports:PortCloudAttribute
    PortDNS = neutron.objects.ports:PortDNS
    PortSecurity = neutron.objects.port.extensions.port_security:PortSecurity
    ProviderResourceAssociation = neutron.objects.servicetype:ProviderResourceAssociation
    ProvisioningBlock = neutron.objects.provisioning_blocks:ProvisioningBlock
    QosBandwidthLimitRule = neutron.objects.qos.rule:QosBandwidthLimitRule
    QosDscpMarkingRule = neutron.objects.qos.rule:QosDscpMarkingRule
    QosMinimumBandwidthRule = neutron.objects.qos.rule:QosMinimumBandwidthRule
    QosPacketRateLimitRule = neutron.objects.qos.rule:QosPacketRateLimitRule
    QosPolicy = neutron.objects.qos.policy:QosPolicy
    QosPolicyDefault = neutron.objects.qos.policy:QosPolicyDefault
    QosPolicyFloatingIPBinding = neutron.objects.qos.binding:QosPolicyFloatingIPBinding
    QosPolicyNetworkBinding = neutron.objects.qos.binding:QosPolicyNetworkBinding
    QosPolicyPortBinding = neutron.objects.qos.binding:QosPolicyPortBinding
    QosPolicyRBAC = neutron.objects.qos.policy:QosPolicyRBAC
    QosRule = neutron.objects.qos.rule:QosRule
    QosRuleType = neutron.objects.qos.rule_type:QosRuleType
    QosRuleTypeDriver = neutron.objects.qos.rule_type:QosRuleTypeDriver
    Quota = neutron.objects.quota:Quota
    QuotaUsage = neutron.objects.quota:QuotaUsage
    Reservation = neutron.objects.quota:Reservation
    ResourceDelta = neutron.objects.quota:ResourceDelta
    Route = neutron.objects.subnet:Route
    Router = neutron.objects.router:Router
    RouterExtraAttributes = neutron.objects.router:RouterExtraAttributes
    RouterTotalLimits = neutron.objects.router:RouterTotalLimits
    RouterL3AgentBinding = neutron.objects.l3agent:RouterL3AgentBinding
    RouterPort = neutron.objects.router:RouterPort
    RouterRoute = neutron.objects.router:RouterRoute
    SecurityGroup = neutron.objects.securitygroup:SecurityGroup
    SecurityGroupPortBinding = neutron.objects.ports:SecurityGroupPortBinding
    SecurityGroupRule = neutron.objects.securitygroup:SecurityGroupRule
    SegmentHostMapping = neutron.objects.network:SegmentHostMapping
    ServiceProfile = neutron.objects.flavor:ServiceProfile
    StandardAttribute = neutron.objects.stdattrs:StandardAttribute
    Subnet = neutron.objects.subnet:Subnet
    SubnetPool = neutron.objects.subnetpool:SubnetPool
    SubnetPoolPrefix = neutron.objects.subnetpool:SubnetPoolPrefix
    SubPort = neutron.objects.trunk:SubPort
    SubnetServiceType = neutron.objects.subnet:SubnetServiceType
    Tag = neutron.objects.tag:Tag
    Trunk = neutron.objects.trunk:Trunk


[extract_messages]
keywords = _ gettext ngettext l_ lazy_gettext
mapping_file = babel.cfg
output_file = neutron/locale/neutron.pot

[compile_catalog]
directory = neutron/locale
domain = neutron neutron-log-error neutron-log-info neutron-log-warning

[update_catalog]
domain = neutron
output_dir = neutron/locale
input_file = neutron/locale/neutron.pot

[wheel]
universal = 1
