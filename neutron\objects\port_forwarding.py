# Copyright (c) 2018 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import itertools

import netaddr

from neutron.db.models import l3
from neutron.db.models import port_forwarding as models
from neutron.objects import base
from neutron.objects import common_types
from neutron.objects import router
from neutron_lib import constants as lib_const
from oslo_utils import versionutils
from oslo_versionedobjects import fields as obj_fields

FIELDS_NOT_SUPPORT_FILTER = ['internal_ip_address', 'internal_port']


@base.NeutronObjectRegistry.register
class PortForwarding(base.NeutronDbObject):
    # Version 1.0: Initial version
    # Version 1.1: Add allowed_port_numbers/denied_port_numbers
    # Version 1.2: Change unique constraint
    # Version 1.3: Delete allowed_port_numbers/denied_port_numbers
    # Version 1.4: Add "external_port_range" and "internal_port_range" fields
    # Version 1.5: Remove unique constraint of internal_port_ranges
    VERSION = '1.5'

    db_model = models.PortForwarding

    primary_keys = ['id']
    foreign_keys = {'FloatingIP': {'floatingip_id': 'id'},
                    'Port': {'internal_port_id': 'id'}}

    fields_need_translation = {
        'internal_port_id': 'internal_neutron_port_id'
    }

    fields = {
        'id': common_types.UUIDField(),
        'floatingip_id': common_types.UUIDField(nullable=False),
        'external_port': common_types.PortRangeField(nullable=True),
        'external_port_range': common_types.PortRangesField(nullable=True),
        'protocol': common_types.IpProtocolEnumField(nullable=False),
        'internal_port_id': common_types.UUIDField(nullable=False),
        'internal_ip_address': obj_fields.IPV4AddressField(),
        'internal_port': common_types.PortRangeField(nullable=True),
        'internal_port_range': common_types.PortRangesField(nullable=True),
        'floating_ip_address': obj_fields.IPV4AddressField(),
        'router_id': common_types.UUIDField()
    }

    synthetic_fields = ['floating_ip_address', 'router_id']
    fields_no_update = {
        'id', 'floatingip_id'
    }

    def __eq__(self, other):
        for attr in self.fields:
            if getattr(self, attr) != getattr(other, attr):
                return False
        return True

    def obj_load_attr(self, attrname):
        if attrname in ['floating_ip_address', 'router_id']:
            return self._load_attr_from_fip(attrname)
        super(PortForwarding, self).obj_load_attr(attrname)

    def _load_attr_from_fip(self, attrname):
        # get all necessary info from fip obj
        fip_obj = router.FloatingIP.get_object(
            self.obj_context, id=self.floatingip_id)
        value = getattr(fip_obj, attrname)
        setattr(self, attrname, value)
        self.obj_reset_changes([attrname])

    def from_db_object(self, db_obj):
        super(PortForwarding, self).from_db_object(db_obj)
        self._load_attr_from_fip(attrname='router_id')
        self._load_attr_from_fip(attrname='floating_ip_address')

    def obj_make_compatible(self, primitive, target_version):
        _target_version = versionutils.convert_version_to_tuple(target_version)
        if _target_version < (1, 4):
            primitive['internal_port'] = int(
                str(primitive.pop(
                    'internal_port_range',
                    str(primitive.get('internal_port', '')))).split(':')[0])
            primitive['external_port'] = int(
                str(primitive.pop(
                    'external_port_range',
                    str(primitive.get('external_port', '')))).split(':')[0])

    @staticmethod
    def _modify_single_ports_to_db(result):
        internal_port = result.pop('internal_port', None)
        external_port = result.pop('external_port', None)
        if internal_port:
            result['internal_port_start'] = internal_port
            result['internal_port_end'] = internal_port

        if external_port:
            result['external_port_start'] = external_port
            result['external_port_end'] = external_port

    @staticmethod
    def _modify_ports_range_to_db(result):
        internal_port_range = result.pop('internal_port_range', None)
        external_port_range = result.pop('external_port_range', None)
        if internal_port_range:
            if isinstance(internal_port_range, list):
                internal_port_range = internal_port_range[0]
            if isinstance(internal_port_range,
                          int) or internal_port_range.isdigit():
                start = end = str(internal_port_range)

            else:
                start, end = internal_port_range.split(':')

            result['internal_port_start'] = start
            result['internal_port_end'] = end

        if external_port_range:
            if isinstance(external_port_range, list):
                external_port_range = external_port_range[0]
            if isinstance(external_port_range,
                          int) or external_port_range.isdigit():
                start = end = str(external_port_range)

            else:
                start, end = external_port_range.split(':')

            result['external_port_start'] = start
            result['external_port_end'] = end

    @staticmethod
    def _modify_ports_range_from_db(result,
                                    internal_port_start=None,
                                    internal_port_end=None,
                                    external_port_start=None,
                                    external_port_end=None):

        if not internal_port_start or not external_port_start:
            return

        result['external_port_range'] = '%s:%s' % (external_port_start,
                                                   external_port_end)
        result['internal_port_range'] = '%s:%s' % (internal_port_start,
                                                   internal_port_end)

    @staticmethod
    def _modify_single_ports_from_db(result,
                                     internal_port_start=None,
                                     internal_port_end=None,
                                     external_port_start=None,
                                     external_port_end=None):

        if not internal_port_start or not external_port_start:
            return
        if internal_port_start == internal_port_end:
            result['internal_port'] = int(internal_port_start)

        if external_port_start == external_port_end:
            result['external_port'] = int(external_port_start)

    @classmethod
    def modify_fields_from_db(cls, db_obj):
        result = super(PortForwarding, cls).modify_fields_from_db(db_obj)
        if 'internal_ip_address' in result:
            result['internal_ip_address'] = netaddr.IPAddress(
                result['internal_ip_address'], version=lib_const.IP_VERSION_4)

        external_port_start = db_obj.get('external_port_start')
        external_port_end = db_obj.get('external_port_end')
        internal_port_start = db_obj.get('internal_port_start')
        internal_port_end = db_obj.get('internal_port_end')

        cls._modify_single_ports_from_db(
            result,
            internal_port_start=internal_port_start,
            external_port_start=external_port_start,
            internal_port_end=internal_port_end,
            external_port_end=external_port_end)
        cls._modify_ports_range_from_db(
            result,
            internal_port_start=internal_port_start,
            external_port_start=external_port_start,
            internal_port_end=internal_port_end,
            external_port_end=external_port_end)
        return result

    @classmethod
    def modify_fields_to_db(cls, fields):
        result = super(PortForwarding, cls).modify_fields_to_db(fields)
        cls._modify_single_ports_to_db(result)
        cls._modify_ports_range_to_db(result)
        if 'internal_ip_address' in result:
            if isinstance(result['internal_ip_address'], list):
                result['internal_ip_address'] = list(
                    map(str, result['internal_ip_address']))
            else:
                result['internal_ip_address'] = str(
                    result['internal_ip_address'])

        return result

    @classmethod
    def get_port_forwarding_obj_by_routers(cls, context, router_ids):
        query = context.session.query(cls.db_model, l3.FloatingIP)
        query = query.join(l3.FloatingIP,
                           cls.db_model.floatingip_id == l3.FloatingIP.id)
        query = query.filter(l3.FloatingIP.router_id.in_(router_ids))

        return cls._unique_port_forwarding_iterator(query)

    @classmethod
    def _unique_port_forwarding_iterator(cls, query):
        q = query.order_by(l3.FloatingIP.router_id)
        keyfunc = lambda row: row[1]
        group_iterator = itertools.groupby(q, keyfunc)

        for key, value in group_iterator:
            for row in value:
                yield (row[1]['router_id'], row[1]['floating_ip_address'],
                       row[0]['id'], row[1]['id'])
