#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api.definitions import subnet
from neutron_lib import constants

IP_ADDR_SIZE = 64

ALIAS = 'subnet-dhcp-ips'
IS_SHIM_EXTENSION = False
IS_STANDARD_ATTR_EXTENSION = False
NAME = 'Subnet DHCP IPs'
DESCRIPTION = 'Add DHCP IPs to subnet'
UPDATED_TIMESTAMP = '2024-10-21T10:00:00-00:00'
RESOURCE_ATTRIBUTE_MAP = {
    subnet.COLLECTION_NAME: {
        'dhcp_ips': {
            'allow_post': True,
            'allow_put': True,
            'is_visible': True,
            'validate': {'type:list_of_unique_strings': IP_ADDR_SIZE},
            'default': constants.ATTR_NOT_SPECIFIED,
        }
    }
}

SUB_RESOURCE_ATTRIBUTE_MAP = {}
ACTION_MAP = {}
REQUIRED_EXTENSIONS = []
OPTIONAL_EXTENSIONS = []
ACTION_STATUS = {}
