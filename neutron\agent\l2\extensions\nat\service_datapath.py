#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import itertools

import netaddr
from neutron_lib.agent import l2_extension as l2_agent_extension
from neutron_lib import constants
from neutron_lib import context as n_context
from neutron_lib.plugins import utils as p_utils
from oslo_concurrency import lockutils
from oslo_config import cfg
from oslo_log import log as logging
from oslo_service import loopingcall

from ryu.base import app_manager
from ryu.controller import handler
from ryu.controller import ofp_event
from ryu.lib.packet import arp
from ryu.lib.packet import ether_types
from ryu.lib.packet import ethernet
from ryu.lib.packet import icmpv6
from ryu.lib.packet import ipv6
from ryu.lib.packet import packet
from ryu.lib.packet import vlan
from ryu.ofproto import inet
from ryu.ofproto import ofproto_v1_3

from neutron.agent.common import flows_process
from neutron.agent.common import ovs_lib
from neutron.agent.linux.openvswitch_firewall import exceptions
from neutron.api.rpc.callbacks.consumer import registry
from neutron.api.rpc.callbacks import events
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import resources_rpc
from neutron.common import coordination
from neutron.common import rpc as n_rpc
from neutron.common import utils as n_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_const

LOG = logging.getLogger(__name__)


def get_tag_from_other_config(bridge, port_name):
    """Return tag stored in OVSDB other_config metadata.

    :param bridge: OVSBridge instance where port is.
    :param port_name: Name of the port.
    :raises OVSFWTagNotFound: In case tag cannot be found in OVSDB.
    """
    other_config = None
    try:
        other_config = bridge.db_get_val(
            'Port', port_name, 'other_config')
        return int(other_config['tag'])
    except (KeyError, TypeError, ValueError):
        raise exceptions.OVSFWTagNotFound(
            port_name=port_name, other_config=other_config)


class NetworkingPathExtensionPortInfoAPI(object):

    def __init__(self, cache_api):
        self.cache_api = cache_api

    def get_provider_ip(self, port_detail):
        port_obj = self.cache_api.get_resource_by_id(
            resources.PORT, port_detail['port_id'])

        if not port_obj:
            return

        fixed_ips = []
        for ip in port_obj.fixed_ips:
            subnet = self.cache_api.get_resource_by_id(
                resources.SUBNET, ip['subnet_id'])
            if subnet.network_id != port_obj.network_id:
                fixed_ips.append((str(ip.ip_address), subnet.id))
        return fixed_ips


class ARPProcessor(app_manager.RyuApp):
    OFP_VERSIONS = [ofproto_v1_3.OFP_VERSION]
    GATEWAY_MAC = None

    def __init__(self, extension, *args, **kwargs):
        super(ARPProcessor, self).__init__(*args, **kwargs)
        self.extension = extension

    @handler.set_ev_cls(ofp_event.EventOFPPacketIn, handler.MAIN_DISPATCHER)
    def _packet_in_handler(self, ev):
        msg = ev.msg
        datapath = msg.datapath
        ofproto = datapath.ofproto

        if msg.reason == ofproto.OFPR_NO_MATCH:
            reason = 'NO MATCH'
        elif msg.reason == ofproto.OFPR_ACTION:
            reason = 'ACTION'
        elif msg.reason == ofproto.OFPR_INVALID_TTL:
            reason = 'INVALID TTL'
        else:
            reason = 'unknown'

        of_in_port = msg.match['in_port']
        LOG.info("Gateway ARP response packet_in port: %s", of_in_port)
        pkt = packet.Packet(data=msg.data)

        LOG.debug('OFPPacketIn received: '
                  'buffer_id=%x total_len=%d reason=%s '
                  'table_id=%d cookie=%d match=%s pkt=%s',
                  msg.buffer_id, msg.total_len, reason,
                  msg.table_id, msg.cookie, msg.match,
                  pkt)

        eth_pkt = pkt.get_protocol(ethernet.ethernet)
        LOG.info("Gateway IP ARP response MAC: %s", eth_pkt.src)
        self.GATEWAY_MAC = str(eth_pkt.src)

        vlan_pkt = pkt.get_protocol(vlan.vlan)
        try:
            if not vlan_pkt or vlan_pkt.vid not in [
                    self.extension.pfn_segmentation_id,
                    self.extension.pfn_segmentation_id_v6]:
                return
            arp_pkt = pkt.get_protocol(arp.arp)
            if arp_pkt:
                if (arp_pkt.opcode == arp.ARP_REPLY and
                        str(arp_pkt.src_ip) in self.extension.route_nexthops):
                    self.extension.learnt_path_gateway_mac = (
                        str(arp_pkt.src_mac))
                    self.extension.install_destination_mac_change_flows(
                        arp_pkt.src_ip, arp_pkt.src_mac)
                return

            ip6_pkt = pkt.get_protocol(ipv6.ipv6)
            icmpv6_pkt = pkt.get_protocol(icmpv6.icmpv6)
            if ip6_pkt and icmpv6_pkt:
                if icmpv6_pkt.type_ == icmpv6.ND_NEIGHBOR_ADVERT:
                    na = icmpv6_pkt.data
                    if (isinstance(na, icmpv6.nd_neighbor) and
                            isinstance(na.option, icmpv6.nd_option_tla)):
                        self.extension.install_destination_mac_change_flows(
                            ip6_pkt.src, na.option.hw_src)
                elif icmpv6_pkt.type_ == icmpv6.ND_NEIGHBOR_SOLICIT:
                    na = icmpv6_pkt.data
                    if (isinstance(na, icmpv6.nd_neighbor) and
                            isinstance(na.option, icmpv6.nd_option_sla)):
                        self.extension.response_ip6_na(na.option.hw_src,
                                                       na.dst,
                                                       ip6_pkt.src)
        except Exception as e:
            LOG.warning("ARP/NDP Processor failed to process packet-in: %s", e)

    def get_arp_packet(self, src_mac, src_ip, arp_op=arp.ARP_REQUEST,
                       dest_ip=None,
                       vlan_id=None,
                       dst_mac=None):
        ret_pkt = packet.Packet()
        ret_pkt.add_protocol(ethernet.ethernet(
            ethertype=(ether_types.ETH_TYPE_8021Q if vlan_id else
                       ether_types.ETH_TYPE_ARP),
            dst=(dst_mac or 'ff:ff:ff:ff:ff:ff'),
            src=src_mac))
        if vlan_id:
            ret_pkt.add_protocol(
                vlan.vlan(vid=vlan_id,
                          ethertype=ether_types.ETH_TYPE_ARP))
        dst_mac = (dst_mac or 'ff:ff:ff:ff:ff:ff')
        ret_pkt.add_protocol(
            arp.arp(opcode=arp_op,
                    src_mac=src_mac,
                    src_ip=src_ip,
                    dst_mac=dst_mac,
                    dst_ip=(dest_ip if dest_ip else src_ip)))
        return ret_pkt

    def send_arp_normal(self, datapath, src_mac, src_ip, dest_ip, arp_op,
                        vlan_id=None,
                        nic_ofport=None,
                        dst_mac=None):
        ofproto = datapath.ofproto
        parser = datapath.ofproto_parser
        pkt = self.get_arp_packet(src_mac, src_ip, arp_op, dest_ip, vlan_id,
                                  dst_mac)
        pkt.serialize()
        LOG.debug("Gratuitous ARP packet_out %s", (pkt,))
        data = pkt.data
        if nic_ofport:
            actions = [parser.OFPActionOutput(nic_ofport, 0)]
        else:
            actions = [parser.NXActionResubmitTable(
                table_id=p_const.NP_EGRESS_NORMAL)]
        out = parser.OFPPacketOut(datapath=datapath,
                                  buffer_id=ofproto.OFP_NO_BUFFER,
                                  in_port=ofproto.OFPP_CONTROLLER,
                                  actions=actions,
                                  data=data)
        LOG.info("Gratuitous ARP with normal L2/L3 switching.")
        datapath.send_msg(out)

    def calculate_dst_mac_address(self, ipv6_address):
        ipv6_addr = netaddr.IPAddress(ipv6_address)
        addr1 = ''.join(ipv6_addr.bits().split(':'))
        mult_prefix = netaddr.IPAddress("ff02::1:ff00:0000")
        addr2 = ''.join(mult_prefix.bits().split(':'))
        bin_addr = addr2[:104] + addr1[104:]

        new_ipv6_addr = netaddr.IPAddress(int(bin_addr, 2))
        mac_prefix = netaddr.EUI("33:33:00:00:00:00")
        new_mac = (
            int(mac_prefix) +
            int(''.join(new_ipv6_addr.bits().split(':'))[96:], 2))
        dest_mac = netaddr.EUI(new_mac)
        return str(new_ipv6_addr), str(dest_mac)

    def get_ndp_packet(self, src_mac, src_ip, dest_ip,
                       vlan_id=None, ndp_op=icmpv6.ND_NEIGHBOR_SOLICIT,
                       dst_mac=None):
        ret_pkt = packet.Packet()
        if not dst_mac:
            bd_ip, bd_mac = self.calculate_dst_mac_address(dest_ip)
        else:
            bd_ip, bd_mac = dest_ip, dst_mac
        ret_pkt.add_protocol(ethernet.ethernet(
            ethertype=(ether_types.ETH_TYPE_8021Q if vlan_id else
                       ether_types.ETH_TYPE_IPV6),
            dst=bd_mac,
            src=src_mac))
        if vlan_id:
            ret_pkt.add_protocol(
                vlan.vlan(vid=vlan_id,
                          ethertype=ether_types.ETH_TYPE_IPV6))
        ipv6_hdr = ipv6.ipv6(
            src=src_ip,
            dst=bd_ip,
            nxt=inet.IPPROTO_ICMPV6
        )
        ret_pkt.add_protocol(ipv6_hdr)

        if ndp_op == icmpv6.ND_NEIGHBOR_SOLICIT:
            option = icmpv6.nd_option_sla(hw_src=src_mac)
            tgt_dst = dest_ip
            res = 0
        else:
            option = icmpv6.nd_option_tla(hw_src=src_mac)
            tgt_dst = src_ip
            res = 3

        icmpv6_hdr = icmpv6.icmpv6(
            type_=ndp_op,
            data=icmpv6.nd_neighbor(
                res=res,
                dst=tgt_dst,
                option=option)
        )
        ret_pkt.add_protocol(icmpv6_hdr)

        return ret_pkt

    def send_ns_normal(self, datapath, src_mac, src_ip, dest_ip, vlan_id=None,
                       nic_ofport=None):
        ofproto = datapath.ofproto
        parser = datapath.ofproto_parser
        pkt = self.get_ndp_packet(src_mac, src_ip, dest_ip, vlan_id)
        pkt.serialize()
        LOG.debug("Gratuitous neighbor solicitation packet_out %s", (pkt,))
        data = pkt.data
        if nic_ofport:
            actions = [parser.OFPActionOutput(nic_ofport, 0)]
        else:
            actions = [parser.NXActionResubmitTable(
                table_id=p_const.NP_EGRESS_NORMAL)]
        out = parser.OFPPacketOut(datapath=datapath,
                                  buffer_id=ofproto.OFP_NO_BUFFER,
                                  in_port=ofproto.OFPP_CONTROLLER,
                                  actions=actions,
                                  data=data)
        LOG.info("Gratuitous neighbor solicitation "
                 "with normal L2/L3 switching.")
        datapath.send_msg(out)

    def send_na_normal(self, datapath, src_mac, src_ip, vlan_id=None,
                       dst_mac=None, dest_ip=None,
                       nic_ofport=None):
        ofproto = datapath.ofproto
        parser = datapath.ofproto_parser
        pkt = self.get_ndp_packet(src_mac, src_ip, dest_ip or src_ip,
                                  vlan_id, ndp_op=icmpv6.ND_NEIGHBOR_ADVERT,
                                  dst_mac=dst_mac)
        pkt.serialize()
        LOG.debug("Gratuitous neighbor advertisment packet_out %s", (pkt,))
        data = pkt.data
        if nic_ofport:
            actions = [parser.OFPActionOutput(nic_ofport, 0)]
        else:
            actions = [parser.NXActionResubmitTable(
                table_id=p_const.NP_EGRESS_NORMAL)]
        out = parser.OFPPacketOut(datapath=datapath,
                                  buffer_id=ofproto.OFP_NO_BUFFER,
                                  in_port=ofproto.OFPP_CONTROLLER,
                                  actions=actions,
                                  data=data)
        LOG.info("Gratuitous neighbor advertisment "
                 "with normal L2/L3 switching.")
        datapath.send_msg(out)


class ServiceDataPathAgentExtension(
        l2_agent_extension.L2AgentExtension,
        flows_process.BridgeNetworkingDataPathFlows):

    PATH_INFO_CACHE = {}
    PORT_INFO_CACHE = {}

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        self.ext_api = NetworkingPathExtensionPortInfoAPI(self.rcache_api)
        self.int_br = self.agent_api.request_int_br()

        self.app_mgr = app_manager.AppManager.get_instance()
        try:
            # Idempotent for extension initialize:
            # Ryu app_manager will check if the app is already in the list.
            # So remove it first without error check.
            self.app_mgr.uninstantiate(ARPProcessor.__name__)
        except Exception as e:
            LOG.info("Ryu app_manager uninstantiate app %s failure: %s",
                     ARPProcessor.__name__, e)
        self.provider_arp = self.app_mgr.instantiate(ARPProcessor, self)
        self.provider_arp.start()

        self.privatefloating_mac = None
        self.privatefloating_ip = None
        self.privatefloating_ip_6 = None
        self.pfn_segmentation_id_v6 = None
        self.pfn_segmentation_id = None
        self.bridge_vlan_mappings = None
        self.phy_br = None
        self.pfn_init_loop = loopingcall.FixedIntervalLoopingCall(
            self.init_privatefloating_info)
        self.pfn_init_loop.start(
            interval=cfg.CONF.AGENT.retrieve_pfn_interval,
            stop_on_exception=False)

        self.resource_rpc = resources_rpc.ResourcesPullRpcApi()
        self._register_rpc_consumers()

        self.learnt_path_gateway_mac = None

        self.enable_private_floating = False

    @lockutils.synchronized('service-path-port-info-cache')
    def set_port_info_cache(self, port_id, port_info):
        self.PORT_INFO_CACHE[port_id] = port_info

    @lockutils.synchronized('service-path-port-info-cache')
    def get_port_info_from_cache(self, port_id):
        return self.PORT_INFO_CACHE.pop(port_id, None)

    @lockutils.synchronized('service-path-port-info-cache')
    def list_port_info_from_cache(self):
        for port_info in self.PORT_INFO_CACHE.values():
            yield port_info

    @lockutils.synchronized('service-path-subnet-reg_id')
    def allocate_reg_id_for_subnet(self):
        return self.reg_id_generator.next()

    def _handle_notification(self, context, resource_type,
                             subnets, event_type):
        if event_type == events.DELETED:
            LOG.debug("Get subnet %s delete notification, "
                      "do nothing to avoid mis-operation. "
                      "If deletion is intentionally, please restart agent "
                      "to re-process the ports and service path again.",
                      subnets)
            return
        for subnet in subnets:
            if subnet.network_id == self.privatefloating_network['id']:
                self._process_subnet_event(
                    context, subnet, event_type)

    def _register_rpc_consumers(self):
        registry.register(self._handle_notification,
                          resources.SUBNET)

        self._connection = n_rpc.Connection()
        endpoints = [resources_rpc.ResourcesPushRpcCallback()]
        topic = resources_rpc.resource_type_versioned_topic(
            resources.SUBNET)
        self._connection.create_consumer(topic, endpoints, fanout=True)
        self._connection.consume_in_threads()

    def consume_api(self, agent_api):
        """Allows an extension to gain access to resources internal to the
           neutron agent and otherwise unavailable to the extension.
        """
        self.agent_api = agent_api
        if agent_api.phys_ofports is None:
            raise Exception("Could not initialize agent "
                            "extension service_datapath")

        self.plugin_rpc = agent_api.plugin_rpc
        self.rcache_api = agent_api.plugin_rpc.remote_resource_cache

    def init_privatefloating_info(self):
        LOG.info("Staring initiate the ServiceDataPathAgentExtension "
                 "informations.")
        self.context = n_context.get_admin_context_without_session()

        self.agent_id = 'ovs-agent-%s' % cfg.CONF.host
        self.privatefloating_info = self.plugin_rpc.get_privatefloating_info(
            self.context, agent_id=self.agent_id, host=cfg.CONF.host,
            availability_zone=cfg.CONF.AGENT.availability_zone)
        LOG.info("Get private floating info: %s", self.privatefloating_info)
        if self.privatefloating_info:
            self.enable_private_floating = \
                self.privatefloating_info.get('privatefloating_enable')
        self.privatefloating_network = self.privatefloating_info.get(
            'privatefloating_network', {})
        if (not self.enable_private_floating or
                not self.privatefloating_network):
            LOG.warning("Neutron server side does not "
                        "enable private floating network! Retry...")
            return False

        self.pfn_segmentation_id = self.privatefloating_network.get(
            'provider:segmentation_id')
        self.bridge_vlan_mappings = p_utils.parse_network_vlan_ranges(
            cfg.CONF.OVS.bridge_vlan_mappings)

        pfn_v6_net = self.privatefloating_info.get(
            'privatefloating_network_v6', {})
        if pfn_v6_net:
            self.pfn_segmentation_id_v6 = (
                pfn_v6_net.get('provider:segmentation_id'))
        else:
            self.pfn_segmentation_id_v6 = self.pfn_segmentation_id

        self.privatefloating_port = self.privatefloating_info.get(
            'privatefloating_port', {})

        self.physnet = self.privatefloating_network.get(
            'provider:physical_network')
        self.phy_br = self.agent_api.request_physical_br(self.physnet)

        vlan_range = n_utils.is_segment_id_in_vlan_range(
            self.pfn_segmentation_id, self.bridge_vlan_mappings)
        if vlan_range:
            self.phy_br = self.agent_api.request_physical_br_with_vlan_range(
                vlan_range)
            port_name = p_utils.get_interface_name(
                self.phy_br.br_name, prefix=p_const.PEER_INTEGRATION_PREFIX)
            self.ofport_int_to_phy = self.int_br.get_port_ofport(port_name)
            phys_if_name = p_utils.get_interface_name(
                self.phy_br.br_name, prefix=p_const.PEER_PHYSICAL_PREFIX)
            self.ofport_phy_to_int = self.phy_br.get_port_ofport(phys_if_name)
        else:
            bridge = self.agent_api.bridge_mappings.get(self.physnet)
            port_name = p_utils.get_interface_name(
                bridge, prefix=p_const.PEER_INTEGRATION_PREFIX)
            self.ofport_int_to_phy = self.int_br.get_port_ofport(port_name)
            self.ofport_phy_to_int = self.agent_api.phys_ofports[self.physnet]

        self.set_path_br(self.phy_br)

        if not cfg.CONF.SERVICEPATH.path_physical_dev:
            LOG.warning('Physical device on bridge %s for service '
                        'data path was not set.', self.phy_br.br_name)
            self.phy_of_port = 0
        else:
            self.phy_of_port = self.phy_br.get_port_ofport(
                cfg.CONF.SERVICEPATH.path_physical_dev)
            if self.phy_of_port <= 0:
                LOG.warning('Physical device %s of_port on path '
                            'bridge not found.',
                            cfg.CONF.SERVICEPATH.path_physical_dev)
                self.phy_of_port = 0

        self.phy_br.arp_direct(self.pfn_segmentation_id)
        self.phy_br.ingress_direct(self.pfn_segmentation_id)

        self.ndp_direct_ip6(self.pfn_segmentation_id_v6)
        self.ingress_direct_ip6(self.pfn_segmentation_id_v6)

        for ip in self.privatefloating_port['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_4:
                self.privatefloating_ip = ip['ip_address']
                break
        if not self.privatefloating_ip:
            LOG.warning('Host private floating port %s has no IP address',
                        self.privatefloating_port['id'])

        self.privatefloating_mac = (
            self.privatefloating_port.get('mac_address'))

        for ip in self.privatefloating_port['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_6:
                self.privatefloating_ip_6 = ip['ip_address']
                self.ndp_op_to_controller_ip6(self.pfn_segmentation_id_v6,
                                              self.privatefloating_ip_6)
                self.install_ndp_responder(
                    self.pfn_segmentation_id_v6,
                    self.privatefloating_ip_6,
                    self.privatefloating_mac)
                break

        self.reg_id_generator = itertools.count(1)
        self.subnet_routes = {}
        self.route_destinations = set()
        self.route_nexthops = set()
        for sub in self.privatefloating_network.get('subnets_detail', []):
            routes = set()
            for route in sub['host_routes']:
                routes.add((str(route['nexthop']), str(route['destination'])))
                self.route_destinations.add(str(route['destination']))
                self.route_nexthops.add(str(route['nexthop']))
            reg_id = self.allocate_reg_id_for_subnet()
            self.subnet_routes[sub['id']] = (routes, reg_id, sub['cidr'])

        self.init_path_flows()
        self.init_service_path_base_pipeline_flows()

        if not cfg.CONF.SERVICEPATH.path_gateway_mac:
            self.mac_learning = loopingcall.FixedIntervalLoopingCall(
                self.send_arp_to_route_destination)
            self.mac_learning.start(
                interval=cfg.CONF.SERVICEPATH.mac_learning_interval,
                initial_delay=cfg.CONF.SERVICEPATH.mac_learning_interval)
        else:
            self.apply_arp_to_route_destination()

        self.pfn_init_loop.stop()

        LOG.info("ServiceDataPathAgentExtension informations "
                 "initiate completely.")
        return True

    def dump_and_clean_stale_flows(self):
        table = p_const.NP_EGRESS_DEST_MAC_LEARN
        cookies = (set([f.cookie for f in self.phy_br.dump_flows(table)]) -
                   self.phy_br.reserved_cookies)
        for c in cookies:
            LOG.warning("Deleting flow with cookie 0x%(cookie)x",
                        {'cookie': c})
            self.phy_br.uninstall_flows(
                table_id=table,
                cookie=c, cookie_mask=ovs_lib.UINT64_BITMASK)

    def init_service_path_base_pipeline_flows(self):
        self.phy_br.install_drop(table_id=p_const.NP_INGRESS_ALLOWED_DIRECT)
        self.phy_br.install_drop(table_id=p_const.NP_INGRESS_ALLOWED_SOURCES)
        self.phy_br.install_goto(
            dest_table_id=p_const.NP_INGRESS_ALLOWED_SOURCES,
            table_id=p_const.NP_INGRESS_DST_DIRECT)

        if cfg.CONF.SERVICEPATH.bond_slave_names:
            (_dp, ofp, ofpp) = self.phy_br._get_dp()
            actions = []
            for slave in cfg.CONF.SERVICEPATH.bond_slave_names:
                ofport = self.phy_br.get_port_ofport(slave)
                actions.append(ofpp.OFPActionOutput(ofport, 0))
            instructions = [ofpp.OFPInstructionActions(
                            ofp.OFPIT_APPLY_ACTIONS, actions)]
            self.phy_br.install_instructions(
                table_id=p_const.NP_EGRESS_NORMAL,
                instructions=instructions)
        else:
            self.phy_br.install_normal(table_id=p_const.NP_EGRESS_NORMAL)

    def apply_arp_to_route_destination(self):
        for routes, reg_id, _cidr in self.subnet_routes.values():
            for nexthop, dest in routes:
                dest_ip = netaddr.IPNetwork(dest)
                if dest_ip.version == constants.IP_VERSION_6:
                    self.change_dest_mac_ip6(
                        dest, cfg.CONF.SERVICEPATH.path_gateway_mac)
                    if cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
                        self.change_dest_mac_ip6(
                            nexthop, cfg.CONF.SERVICEPATH.path_gateway_mac)
                else:
                    self.change_dest_mac(
                        dest, reg_id,
                        cfg.CONF.SERVICEPATH.path_gateway_mac)
                    if cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
                        self.change_dest_mac(
                            nexthop, reg_id,
                            cfg.CONF.SERVICEPATH.path_gateway_mac)

    @coordination.synchronized('{f_name}-{subnet.network_id}')
    def _process_subnet_event(self, context, subnet, event_type):
        sub_net_id = getattr(subnet, 'network_id', None)
        if sub_net_id != self.privatefloating_network['id']:
            return
        if event_type == events.UPDATED:
            LOG.info("Get subnet %s update notification, "
                     "updating service path...", subnet)

            new_nexthops = set()
            new_destinations = set()
            new_routes = set()
            for route in subnet.host_routes:
                new_nexthops.add(str(route['nexthop']))
                new_destinations.add(str(route['destination']))
                new_routes.add((str(route['nexthop']),
                                str(route['destination'])))

            existing_destinations = []
            for routes, _reg_id, _cidr in self.subnet_routes.values():
                for _nexthop, destination in routes:
                    existing_destinations.append(destination)

            removed_route = set()
            # routes removed
            for dest_ip in self.route_destinations - new_destinations:
                if existing_destinations.count(dest_ip) > 1:
                    # more than one routes to same dest_ip
                    continue
                removed_route.add(dest_ip)
                for port_info in self.list_port_info_from_cache():
                    self.remove_routes_flows([dest_ip], port_info)
                    self._remove_path_flows(dest_ip)

                if cfg.CONF.SERVICEPATH.allow_icmp_to_path_destination:
                    ip_addr = netaddr.IPNetwork(dest_ip)
                    if ip_addr.version == constants.IP_VERSION_6:
                        self.remove_path_destination_icmp_classify_ip6(dest_ip)
                    else:
                        self.remove_path_destination_icmp_classify(dest_ip)

            # routes added
            for dest_ip in new_destinations - self.route_destinations:
                for port_info in self.list_port_info_from_cache():
                    self.install_routes_flows([dest_ip], port_info)

                if cfg.CONF.SERVICEPATH.allow_icmp_to_path_destination:
                    ip_addr = netaddr.IPNetwork(dest_ip)
                    if ip_addr.version == constants.IP_VERSION_6:
                        self.path_destination_icmp_classify_ip6(dest_ip)
                    else:
                        self.path_destination_icmp_classify(dest_ip)

            removed_nexthops = set()
            for next_hop in self.route_nexthops - new_nexthops:
                removed_nexthops.add(next_hop)
                if not cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
                    continue
                for port_info in self.list_port_info_from_cache():
                    self.remove_routes_flows([next_hop], port_info)
                    self._remove_path_flows(next_hop)
                ip_addr = netaddr.IPAddress(next_hop)
                if ip_addr.version == constants.IP_VERSION_6:
                    self.remove_ndp_op_to_controller_ip6(
                        self.pfn_segmentation_id_v6,
                        ipv6_dst=next_hop)
                    self.remove_path_destination_icmp_classify_ip6(next_hop)
                else:
                    self.phy_br.remove_arp_op_to_controller(
                        self.pfn_segmentation_id, arp.ARP_REPLY, next_hop)
                    self.remove_path_destination_icmp_classify(next_hop)

            if cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
                for next_hop in new_nexthops - self.route_nexthops:
                    for port_info in self.list_port_info_from_cache():
                        self.install_routes_flows([next_hop], port_info)
                    ip_addr = netaddr.IPAddress(next_hop)
                    if ip_addr.version == constants.IP_VERSION_6:
                        self.path_destination_icmp_classify_ip6(next_hop)
                    else:
                        self.path_destination_icmp_classify(next_hop)

            if subnet.id in self.subnet_routes:
                (_r, reg_id, _cidr) = self.subnet_routes.get(subnet.id)
            else:
                reg_id = self.allocate_reg_id_for_subnet()
            self.subnet_routes[subnet.id] = (new_routes, reg_id, subnet.cidr)
            self.route_destinations = (
                (self.route_destinations | new_destinations) - removed_route)
            self.route_nexthops = (
                (self.route_nexthops | new_nexthops) - removed_nexthops)

            self.init_path_flows()

            if not cfg.CONF.SERVICEPATH.path_gateway_mac:
                # learn new routes destination MACs
                self.send_arp_to_route_destination()
            else:
                self.apply_arp_to_route_destination()

    def send_arp_to_route_destination(self):
        dp, _ofp, _ofpp = self.phy_br._get_dp()
        for nexthop in self.route_nexthops:
            ip_next = netaddr.IPNetwork(nexthop)
            if ip_next.version == constants.IP_VERSION_6:
                if (not self.privatefloating_mac or
                        not self.privatefloating_ip_6 or
                        not self.pfn_segmentation_id_v6):
                    continue
                self.ndp_op_to_controller_ip6(self.pfn_segmentation_id_v6,
                                              ipv6_dst=nexthop)
                self.provider_arp.send_ns_normal(
                    dp, self.privatefloating_mac, self.privatefloating_ip_6,
                    nexthop,
                    vlan_id=self.pfn_segmentation_id_v6)
            else:
                if (not self.privatefloating_mac or
                        not self.privatefloating_ip or
                        not self.pfn_segmentation_id):
                    continue
                self.phy_br.arp_op_to_controller(
                    self.pfn_segmentation_id, arp.ARP_REPLY, nexthop)
                self.provider_arp.send_arp_normal(
                    dp, self.privatefloating_mac, self.privatefloating_ip,
                    nexthop,
                    arp_op=arp.ARP_REQUEST,
                    vlan_id=self.pfn_segmentation_id)

        self.dump_and_clean_stale_flows()

    def install_destination_mac_change_flows(self, nexthop, dest_mac):
        for routes, reg_id, cidr in self.subnet_routes.values():
            nexthop_ip = netaddr.IPAddress(nexthop)
            if nexthop in netaddr.IPNetwork(cidr):
                for next_hop_ip, dest in routes:
                    dest_ip = netaddr.IPNetwork(dest)
                    if (nexthop_ip.version == constants.IP_VERSION_6 and
                            dest_ip.version == constants.IP_VERSION_6):
                        self.change_dest_mac_ip6(dest, dest_mac)
                        if cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
                            self.change_dest_mac_ip6(next_hop_ip, dest_mac)
                    else:
                        self.change_dest_mac(dest, reg_id, dest_mac)
                        if cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
                            self.change_dest_mac(next_hop_ip, reg_id, dest_mac)

    def init_path_flows(self):
        for dest in self.route_destinations:
            for proto in ['tcp', 'udp', 'icmp']:
                path = {'protocol': proto,
                        'orig_ip': dest,
                        'dest_ip': dest,
                        'mac_address': self.privatefloating_mac}
                self._add_path_flows(path)

        self.init_path_icmp_flows()

    def init_path_icmp_flows(self):
        if not cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
            return

        for nexthop in self.route_nexthops:
            dest_ip = netaddr.IPAddress(nexthop)
            if dest_ip.version == constants.IP_VERSION_6:
                self.path_destination_icmp_classify_ip6(nexthop)
            else:
                self.path_destination_icmp_classify(nexthop)

    def _add_path_flows(self, path):
        dest_net = netaddr.IPNetwork(path['dest_ip'])
        if dest_net.version == constants.IP_VERSION_6:
            self.egress_service_direct_ip6(self.ofport_phy_to_int,
                                           ipv6_dst=path['dest_ip'])
            self.path_classify_service_path_ip6()
            if (path['protocol'] == 'icmp' and
                    cfg.CONF.SERVICEPATH.allow_icmp_to_path_destination):
                self.path_destination_icmp_classify_ip6(path['dest_ip'])
            self.egress_to_physical_nic_ip6(
                path, self.pfn_segmentation_id_v6, self.phy_of_port)
        else:
            self.egress_service_direct(self.ofport_phy_to_int,
                                       ipv4_dst=path['dest_ip'])
            self.path_classify_service_path()
            if (path['protocol'] == 'icmp' and
                    cfg.CONF.SERVICEPATH.allow_icmp_to_path_destination):
                self.path_destination_icmp_classify(path['dest_ip'])
            self.egress_to_physical_nic(
                path, self.pfn_segmentation_id, self.phy_of_port)

    def _remove_path_flows(self, dest_cidr):
        ip_addr = netaddr.IPNetwork(dest_cidr)
        if ip_addr.version == constants.IP_VERSION_6:
            self.remove_egress_service_direct_ip6(
                self.ofport_phy_to_int,
                ipv6_dst=dest_cidr)
            self.remove_learnt_mac_change_flows_ip6(dest_cidr)
        else:
            self.remove_egress_service_direct(self.ofport_phy_to_int,
                                              dest_cidr)
            self.remove_learnt_mac_change_flows(dest_cidr)

    def response_ip6_na(self, dst_mac, src_ip, dst_ip):
        dp, _ofp, _ofpp = self.phy_br._get_dp()
        self.provider_arp.send_na_normal(
            dp, self.privatefloating_mac, src_ip,
            vlan_id=self.pfn_segmentation_id_v6,
            dst_mac=dst_mac,
            dest_ip=dst_ip)

    def send_ip6_na(self, src_mac, src_ip):
        dp, _ofp, _ofpp = self.phy_br._get_dp()
        self.provider_arp.send_na_normal(
            dp, src_mac, src_ip, vlan_id=self.pfn_segmentation_id_v6)

    def send_garp(self, src_mac, src_ip):
        dp, _ofp, _ofpp = self.phy_br._get_dp()
        dst_mac = (cfg.CONF.SERVICEPATH.path_gateway_mac or
                   self.learnt_path_gateway_mac or None)
        # Send 3 times of (request, reply) GARPs.
        for _i in range(3):
            self.provider_arp.send_arp_normal(
                dp, src_mac, src_ip, src_ip, arp.ARP_REQUEST,
                vlan_id=self.pfn_segmentation_id,
                dst_mac=dst_mac)
            self.provider_arp.send_arp_normal(
                dp, src_mac, src_ip, src_ip, arp.ARP_REPLY,
                vlan_id=self.pfn_segmentation_id,
                dst_mac=dst_mac)

    def _check_subnet_overlaps(self, pf_subnet_id, port_fixed_ips):
        # Check whether the port subnet has intersection with
        # private floating subnet. If True, print WARNING log.
        pf_sub = self.rcache_api.get_resource_by_id(resources.SUBNET,
                                                    pf_subnet_id)
        for ip in port_fixed_ips:
            if pf_subnet_id == ip['subnet_id']:
                continue
            port_sub = self.rcache_api.get_resource_by_id(resources.SUBNET,
                                                          ip['subnet_id'])
            pf_cidr = netaddr.IPNetwork(pf_sub['cidr'])
            port_cidr = netaddr.IPNetwork(port_sub['cidr'])
            # check whether private floating subnet
            # overlap with port subnet
            if pf_cidr in port_cidr or port_cidr in pf_cidr:
                LOG.warning(
                    'Port subnet %(p_sub)s overlaps with private floating '
                    'subnet %(pf_sub)s, it may affect the traffic of '
                    'service datapath',
                    {'p_sub': port_sub['cidr'], 'pf_sub': pf_sub['cidr']})
            # check whether private floating subnet route destinations
            # overlap with port subnet
            for dest in self.route_destinations:
                dest_cidr = netaddr.IPNetwork(dest)
                if port_cidr in dest_cidr or dest_cidr in port_cidr:
                    LOG.warning(
                        'Port subnet %(p_sub)s overlaps with private '
                        'floating subnet route destination %(dest)s, '
                        'it may affect the traffic of '
                        'service datapath',
                        {'p_sub': port_sub['cidr'], 'dest': dest})

    def handle_port(self, context, port_detail):
        if not self.enable_private_floating:
            LOG.warning("ServiceDataPathAgentExtension failed to handle_port. "
                        "Neutron server side does not "
                        "enable private floating network! Or the agent "
                        "extension does not initiate done.")
            return

        server_default_az = self.privatefloating_info.get(
            'default_availability_zone')
        az_privatefloating_network = self.privatefloating_info.get(
            'availability_zone_privatefloating_network')
        agent_az = cfg.CONF.AGENT.availability_zone
        if az_privatefloating_network and agent_az not in \
                az_privatefloating_network.keys() and \
                agent_az != server_default_az:
            LOG.warning("Ovs Agent side has not set availability zone or "
                        "it sets wrong, which need to update it.")

        device_owner = port_detail['device_owner']
        compute_device_prefix = constants.DEVICE_OWNER_COMPUTE_PREFIX
        al_device_owners = cfg.CONF.SERVICEPATH.extra_allowed_device_owners
        if (not device_owner.startswith(compute_device_prefix) and
                (not al_device_owners or (al_device_owners and
                     device_owner not in al_device_owners))):
            return

        provider_ips = self.ext_api.get_provider_ip(port_detail)
        if not provider_ips:
            LOG.info("Failed to get port %s instance provider IP info.",
                     port_detail['port_id'])
            return

        try:
            port = port_detail['vif_port']
            vlan = get_tag_from_other_config(self.int_br, port.port_name)
            port_info = {"port_id": port_detail['port_id'],
                         "device_owner": device_owner,
                         "port_name": port.port_name,
                         "vlan": vlan,
                         "mac_address": port_detail["mac_address"],
                         "fixed_ips": port_detail["fixed_ips"],
                         "provider_ips": provider_ips,
                         "ofport": port.ofport,
                         "segmentation_id": port_detail['segmentation_id'],
                         "physical_network": port_detail['physical_network']}
            self.set_port_info_cache(port_detail['port_id'], port_info)

            pf_subnet_id = provider_ips[0][1]
            self._check_subnet_overlaps(pf_subnet_id, port_detail["fixed_ips"])
        except Exception as err:
            LOG.info("Failed to set port %s info, error: %s",
                     port_detail['port_id'], err)
        else:
            self.process_install_service_path_flows(port_info)

    def _match_vlan_bridge_output(self, physical_network, segmentation_id):
        need_output = False
        # get port physical bridge
        port_bridge = self.agent_api.bridge_mappings.get(physical_network)
        port_vlan_range = n_utils.is_segment_id_in_vlan_range(
            segmentation_id, self.bridge_vlan_mappings)
        if port_vlan_range:
            port_bridge = n_utils.get_bridge_by_vlan_range(
                self.bridge_vlan_mappings, port_vlan_range)
        if port_bridge != self.phy_br.br_name:
            need_output = True
        return need_output

    def install_routes_flows(self, dest_cidrs, port_info):

        through_sg = cfg.CONF.SERVICEPATH.go_through_security_group
        for dest_cidr in dest_cidrs:
            ip_addr = netaddr.IPNetwork(dest_cidr)
            if ip_addr.version == constants.IP_VERSION_6:
                self.add_flow_int_br_egress_direct_ip6(
                    port_info["ofport"], port_info["vlan"],
                    self.ofport_int_to_phy, dest_cidr,
                    through_sg=through_sg)
                self.add_flow_int_br_ingress_output_ip6(
                    self.ofport_int_to_phy, port_info["vlan"],
                    port_info['mac_address'], port_info["ofport"], dest_cidr,
                    through_sg=through_sg)
                self.egress_service_direct_ip6(self.ofport_phy_to_int,
                                               ipv6_dst=dest_cidr)
                if self._match_vlan_bridge_output(
                        port_info['physical_network'],
                        port_info['segmentation_id']):
                    self.add_service_datapath_int_br_ingress_output_ip6(
                        self.ofport_int_to_phy, port_info["vlan"],
                        port_info['mac_address'], port_info["ofport"],
                        dest_cidr, through_sg=through_sg)
            else:
                self.add_flow_int_br_egress_direct(
                    port_info["ofport"], port_info["vlan"],
                    self.ofport_int_to_phy,
                    ipv4_dst=dest_cidr,
                    through_sg=through_sg)
                self.add_flow_int_br_ingress_output(
                    self.ofport_int_to_phy, port_info["vlan"],
                    port_info['mac_address'], port_info["ofport"],
                    ipv4_src=dest_cidr,
                    through_sg=through_sg)
                self.egress_service_direct(self.ofport_phy_to_int,
                                           ipv4_dst=dest_cidr)
                # In scenarios where the physical bridge of the floating
                # network and the port network is different, add this flow
                # for ingress traffic to direct the output to the VM port.
                # Need in openvswitch firewall, stateless firewall will output
                # in table 81
                if self._match_vlan_bridge_output(
                        port_info['physical_network'],
                        port_info['segmentation_id']):
                    self.add_service_datapath_int_br_ingress_output(
                        self.ofport_int_to_phy, port_info["vlan"],
                        port_info['mac_address'], port_info["ofport"],
                        ipv4_src=dest_cidr,
                        through_sg=through_sg)

    def remove_routes_flows(self, dest_cidrs, port_info):
        through_sg = cfg.CONF.SERVICEPATH.go_through_security_group
        for dest_cidr in dest_cidrs:
            ip_addr = netaddr.IPNetwork(dest_cidr)
            if ip_addr.version == constants.IP_VERSION_6:
                self.remove_flow_int_br_egress_direct_ip6(
                    port_info["ofport"], dest_cidr,
                    through_sg=through_sg)
                self.remove_flow_int_br_ingress_output_ip6(
                    self.ofport_int_to_phy, port_info["vlan"],
                    port_info['mac_address'], dest_cidr,
                    through_sg=through_sg)
                if self._match_vlan_bridge_output(
                        port_info['physical_network'],
                        port_info['segmentation_id']):
                    self.remove_service_datapath_int_br_ingress_output_ip6(
                        self.ofport_int_to_phy, port_info["vlan"],
                        port_info['mac_address'], dest_cidr,
                        through_sg=through_sg)
            else:
                self.remove_ports_service_path_direct_flow(
                    port_info["ofport"],
                    port_info["vlan"],
                    port_info['mac_address'],
                    self.ofport_int_to_phy,
                    orig_ip=dest_cidr,
                    through_sg=through_sg)
                if self._match_vlan_bridge_output(
                        port_info['physical_network'],
                        port_info['segmentation_id']):
                    self.remove_service_datapath_int_br_ingress_output(
                        self.ofport_int_to_phy, port_info["vlan"],
                        port_info['mac_address'], ipv4_src=dest_cidr,
                        through_sg=through_sg)

    def _get_fixed_ip(self, port_info):
        provider_ip_addrs = [provider_ip[0] for
                             provider_ip in port_info['provider_ips']]
        for ip in port_info['fixed_ips']:
            ip_addr = netaddr.IPAddress(ip['ip_address'])
            if ip_addr.version == 4:
                if ip['ip_address'] not in provider_ip_addrs:
                    return ip['ip_address']

    def delete_port(self, context, port_detail):
        if not self.enable_private_floating:
            LOG.warning("ServiceDataPathAgentExtension failed to delete_port. "
                        "Neutron server side does not "
                        "enable private floating network! Or the agent "
                        "extension does not initiate done.")
            return
        port_info = self.get_port_info_from_cache(port_detail['port_id'])
        if not port_info:
            LOG.info("No port_info cache found for %s, "
                     "skipping remove networking path related flows.",
                     port_detail['port_id'])
            return

        self.remove_routes_flows(self.route_destinations, port_info)
        if cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
            self.remove_routes_flows(self.route_nexthops, port_info)

        for ip in port_info['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_6:
                port_fixed_ip = str(ip['ip_address'])
                self.remove_flow_snat_br_src_addr_check_ip6(
                    port_info["vlan"], port_info['mac_address'],
                    port_fixed_ip)
                self.remove_ndp_responder(
                    self.pfn_segmentation_id_v6,
                    port_fixed_ip)
                self.remove_ndp_op_to_controller_ip6(
                    self.pfn_segmentation_id_v6,
                    port_fixed_ip,
                    icmpv6_type=icmpv6.ND_NEIGHBOR_SOLICIT)
                self.remove_ingress_physical_nic_to_int_ip6(
                    self.pfn_segmentation_id_v6, port_fixed_ip)

        port_fixed_ip = self._get_fixed_ip(port_info)
        if not port_fixed_ip:
            return

        for provider_ip_addr, _subnet_id in port_info['provider_ips']:
            self.remove_ports_networking_path_nat_and_arp_flow(
                port_info['vlan'], port_info['mac_address'], port_fixed_ip,
                self.pfn_segmentation_id, provider_ip_addr)

            self.remove_allowed_snat_flows(port_info['vlan'],
                                           port_info['mac_address'])

    def process_install_service_path_flows(self, port_info):
        if not self.phy_br:
            return

        LOG.info("Add service path flows for port %s", port_info['port_id'])
        try:
            self.install_ports_service_path_direct_flows(port_info)
        except Exception as err:
            LOG.debug("Failed to install networking path direct flows "
                      "for port %s, error: %s",
                      port_info['port_id'], err)
        try:
            self.install_ports_serivce_path_flows(port_info)
        except Exception as err:
            LOG.debug("Failed to install networking path nat and "
                      "arp flows for port %s, error: %s",
                      port_info['port_id'], err)

    def get_ofport(self, port_id):
        vifs = self.int_br.get_vif_ports()
        for vif in vifs:
            if vif.vif_id == port_id:
                return vif

    def install_ports_serivce_path_flows(self, port_info):
        provider_port_mac = self.privatefloating_mac

        for ip in port_info['fixed_ips']:
            ip_addr = netaddr.IPNetwork(ip['ip_address'])
            if ip_addr.version == constants.IP_VERSION_6:
                port_fixed_ip = str(ip['ip_address'])
                self.add_flow_snat_br_src_addr_check_ip6(
                    port_info["vlan"], port_info['mac_address'],
                    port_fixed_ip, provider_port_mac)
                self.install_ndp_responder(
                    self.pfn_segmentation_id_v6,
                    port_fixed_ip,
                    provider_port_mac)
                self.ingress_physical_nic_to_int_ip6(
                    port_info["vlan"], self.pfn_segmentation_id_v6,
                    port_fixed_ip, port_info['mac_address'],
                    self.ofport_phy_to_int)
                self.send_ip6_na(provider_port_mac, port_fixed_ip)

        port_fixed_ip = self._get_fixed_ip(port_info)
        if not port_fixed_ip:
            return

        for provider_ip_addr, subnet_id in port_info['provider_ips']:
            routes = self.subnet_routes.get(subnet_id)
            if not routes:
                continue
            _r, reg_id, _cidr = routes

            self.install_arp_responder(
                ip=provider_ip_addr,
                mac=provider_port_mac)

            if cfg.CONF.SERVICEPATH.learn_ingress_direct:
                self.learn_ingress_physical_nic_to_int(
                    self.pfn_segmentation_id, port_info["vlan"],
                    port_info['mac_address'],
                    provider_port_mac,
                    provider_ip_addr, reg_id, port_fixed_ip)
                for cidr in cfg.CONF.SERVICEPATH.allowed_ingress_source_cidrs:
                    self.ingress_physical_allowed_source_to_int(
                        self.pfn_segmentation_id, cidr)
                if cfg.CONF.SERVICEPATH.allowed_ingress_source_cidrs:
                    self.ingress_physical_nic_to_int(
                        port_info["vlan"], self.pfn_segmentation_id,
                        provider_ip_addr,
                        port_info['mac_address'], port_fixed_ip,
                        self.ofport_phy_to_int,
                        table=p_const.NP_INGRESS_ALLOWED_DIRECT)
            else:
                self.add_flow_snat_br_nat(
                    port_info["vlan"], port_info['mac_address'],
                    port_fixed_ip,
                    provider_port_mac, provider_ip_addr,
                    reg_id=reg_id)
                self.ingress_physical_nic_to_int(
                    port_info["vlan"], self.pfn_segmentation_id,
                    provider_ip_addr,
                    port_info['mac_address'], port_fixed_ip,
                    self.ofport_phy_to_int)

            self.install_allowed_snat_flows(
                self.pfn_segmentation_id,
                port_info['vlan'],
                eth_src=port_info['mac_address'],
                mod_eth_src=provider_port_mac,
                ipv4_src=provider_ip_addr,
                reg_id=reg_id)

            try:
                self.send_garp(provider_port_mac, provider_ip_addr)
            except Exception as e:
                LOG.debug("Failed to send GARP for IP %s and MAC %s, "
                          "error: %s",
                          provider_ip_addr, provider_port_mac, e)

    def install_ports_service_path_direct_flows(self, port_info):
        self.install_routes_flows(self.route_destinations, port_info)
        if not cfg.CONF.SERVICEPATH.allow_icmp_to_path_gateway:
            return
        self.install_routes_flows(self.route_nexthops, port_info)
